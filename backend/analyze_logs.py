#!/usr/bin/env python3
"""
毛孩子AI项目日志分析工具
分析项目运行日志，提供统计信息和健康状况报告
"""

import os
import re
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from pathlib import Path
import json


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = Path(log_dir)
        self.stats = {
            "total_lines": 0,
            "log_levels": defaultdict(int),
            "errors": [],
            "warnings": [],
            "recent_activity": [],
            "database_operations": 0,
            "api_requests": 0,
            "service_restarts": 0,
            "performance_issues": []
        }
    
    def analyze_all_logs(self):
        """分析所有日志文件"""
        print("🔍 开始分析毛孩子AI项目日志...")
        print("=" * 50)
        
        # 分析应用日志
        if (self.log_dir / "app.log").exists():
            print("📋 分析应用日志...")
            self.analyze_app_log()
        
        # 分析错误日志
        if (self.log_dir / "error.log").exists():
            print("❌ 分析错误日志...")
            self.analyze_error_log()
        
        # 生成报告
        self.generate_report()
    
    def analyze_app_log(self):
        """分析应用日志"""
        try:
            with open(self.log_dir / "app.log", 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.stats["total_lines"] += len(lines)
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 解析日志级别
                if " | INFO " in line:
                    self.stats["log_levels"]["INFO"] += 1
                elif " | ERROR " in line:
                    self.stats["log_levels"]["ERROR"] += 1
                    self.stats["errors"].append(line)
                elif " | WARNING " in line:
                    self.stats["log_levels"]["WARNING"] += 1
                    self.stats["warnings"].append(line)
                elif " | DEBUG " in line:
                    self.stats["log_levels"]["DEBUG"] += 1
                
                # 检测特定事件
                if "启动毛孩子AI后端服务" in line:
                    self.stats["service_restarts"] += 1
                
                if "数据库" in line:
                    self.stats["database_operations"] += 1
                
                if "慢请求" in line:
                    self.stats["performance_issues"].append(line)
                
                # 记录最近活动（最后10条）
                if len(self.stats["recent_activity"]) < 10:
                    self.stats["recent_activity"].append(line)
                else:
                    self.stats["recent_activity"].pop(0)
                    self.stats["recent_activity"].append(line)
        
        except Exception as e:
            print(f"❌ 分析应用日志失败: {e}")
    
    def analyze_error_log(self):
        """分析错误日志"""
        try:
            with open(self.log_dir / "error.log", 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if line:
                    self.stats["errors"].append(line)
        
        except Exception as e:
            print(f"❌ 分析错误日志失败: {e}")
    
    def generate_report(self):
        """生成分析报告"""
        print("\n📊 日志分析报告")
        print("=" * 50)
        
        # 基本统计
        print(f"📈 总日志行数: {self.stats['total_lines']}")
        print(f"🔄 服务重启次数: {self.stats['service_restarts']}")
        print(f"🗄️  数据库操作: {self.stats['database_operations']}")
        
        # 日志级别统计
        print("\n📋 日志级别分布:")
        for level, count in self.stats["log_levels"].items():
            print(f"  {level}: {count}")
        
        # 错误统计
        if self.stats["errors"]:
            print(f"\n❌ 错误总数: {len(self.stats['errors'])}")
            print("最近的错误:")
            for error in self.stats["errors"][-5:]:  # 显示最后5个错误
                print(f"  • {error}")
        else:
            print("\n✅ 没有发现错误")
        
        # 警告统计
        if self.stats["warnings"]:
            print(f"\n⚠️  警告总数: {len(self.stats['warnings'])}")
            print("最近的警告:")
            for warning in self.stats["warnings"][-3:]:  # 显示最后3个警告
                print(f"  • {warning}")
        else:
            print("\n✅ 没有发现警告")
        
        # 性能问题
        if self.stats["performance_issues"]:
            print(f"\n🐌 性能问题: {len(self.stats['performance_issues'])}")
            for issue in self.stats["performance_issues"]:
                print(f"  • {issue}")
        else:
            print("\n⚡ 没有发现性能问题")
        
        # 最近活动
        print("\n🕒 最近活动:")
        for activity in self.stats["recent_activity"][-5:]:
            print(f"  • {activity}")
        
        # 健康状况评估
        self.assess_health()
    
    def assess_health(self):
        """评估系统健康状况"""
        print("\n🏥 系统健康状况评估")
        print("=" * 30)
        
        health_score = 100
        issues = []
        
        # 检查错误率
        total_logs = sum(self.stats["log_levels"].values())
        if total_logs > 0:
            error_rate = (self.stats["log_levels"]["ERROR"] / total_logs) * 100
            if error_rate > 10:
                health_score -= 30
                issues.append(f"错误率过高: {error_rate:.1f}%")
            elif error_rate > 5:
                health_score -= 15
                issues.append(f"错误率偏高: {error_rate:.1f}%")
        
        # 检查性能问题
        if self.stats["performance_issues"]:
            health_score -= len(self.stats["performance_issues"]) * 10
            issues.append(f"发现 {len(self.stats['performance_issues'])} 个性能问题")
        
        # 检查服务稳定性
        if self.stats["service_restarts"] > 5:
            health_score -= 20
            issues.append(f"服务重启过于频繁: {self.stats['service_restarts']} 次")
        
        # 输出健康状况
        if health_score >= 90:
            status = "🟢 优秀"
        elif health_score >= 70:
            status = "🟡 良好"
        elif health_score >= 50:
            status = "🟠 一般"
        else:
            status = "🔴 需要关注"
        
        print(f"健康评分: {health_score}/100 - {status}")
        
        if issues:
            print("\n发现的问题:")
            for issue in issues:
                print(f"  ⚠️  {issue}")
        else:
            print("\n✅ 系统运行正常，未发现明显问题")
        
        # 建议
        print("\n💡 建议:")
        if self.stats["errors"]:
            print("  • 关注错误日志，及时修复问题")
        if self.stats["performance_issues"]:
            print("  • 优化慢请求，提升响应速度")
        if self.stats["service_restarts"] > 3:
            print("  • 检查服务稳定性，减少重启频率")
        
        print("  • 定期清理日志文件，避免磁盘空间不足")
        print("  • 监控数据库连接状态")
        print("  • 关注API响应时间和错误率")


def main():
    """主函数"""
    analyzer = LogAnalyzer()
    analyzer.analyze_all_logs()


if __name__ == "__main__":
    main()
