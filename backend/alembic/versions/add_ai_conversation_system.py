"""add_ai_conversation_system

Revision ID: add_ai_conversation_system
Revises: 28b9d0f97be1
Create Date: 2025-06-25 18:55:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_ai_conversation_system'
down_revision = '28b9d0f97be1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建conversations表
    op.create_table('conversations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
        sa.Column('pet_id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('message_count', sa.Integer(), nullable=True, default=0),
        sa.Column('total_tokens', sa.Integer(), nullable=True, default=0),
        sa.Column('is_active', sa.<PERSON>(), nullable=True, default=True),
        sa.Column('last_message_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('context_window', sa.Integer(), nullable=True, default=10),
        sa.Column('context_data', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['pet_id'], ['pets.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversations_id'), 'conversations', ['id'], unique=False)
    
    # 更新messages表结构
    # 1. 修改conversation_id字段类型为Integer并添加外键
    op.drop_index('ix_messages_conversation_id', table_name='messages')
    op.drop_column('messages', 'conversation_id')
    op.add_column('messages', sa.Column('conversation_id', sa.Integer(), nullable=False))
    op.create_foreign_key(None, 'messages', 'conversations', ['conversation_id'], ['id'])
    op.create_index(op.f('ix_messages_conversation_id'), 'messages', ['conversation_id'], unique=False)
    
    # 2. 添加AI相关字段
    op.add_column('messages', sa.Column('ai_model', sa.String(length=100), nullable=True))
    op.add_column('messages', sa.Column('ai_prompt', sa.Text(), nullable=True))
    op.add_column('messages', sa.Column('tokens_used', sa.Integer(), nullable=True))
    op.add_column('messages', sa.Column('response_time', sa.Float(), nullable=True))
    op.add_column('messages', sa.Column('confidence_score', sa.Float(), nullable=True))
    
    # 3. 添加情感和行为字段
    op.add_column('messages', sa.Column('mood', sa.String(length=50), nullable=True))
    op.add_column('messages', sa.Column('actions', sa.JSON(), nullable=True))
    op.add_column('messages', sa.Column('emotions', sa.JSON(), nullable=True))
    
    # 4. 更新extra_data字段为JSON类型
    op.drop_column('messages', 'extra_data')
    op.add_column('messages', sa.Column('extra_data', sa.JSON(), nullable=True))
    
    # 5. 添加其他字段
    op.add_column('messages', sa.Column('is_read', sa.Boolean(), nullable=True, default=True))
    op.add_column('messages', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True))


def downgrade() -> None:
    # 删除conversations表
    op.drop_index(op.f('ix_conversations_id'), table_name='conversations')
    op.drop_table('conversations')
    
    # 恢复messages表结构
    op.drop_column('messages', 'updated_at')
    op.drop_column('messages', 'is_read')
    op.drop_column('messages', 'extra_data')
    op.add_column('messages', sa.Column('extra_data', mysql.TEXT(collation='utf8mb4_unicode_ci'), nullable=True))
    op.drop_column('messages', 'emotions')
    op.drop_column('messages', 'actions')
    op.drop_column('messages', 'mood')
    op.drop_column('messages', 'confidence_score')
    op.drop_column('messages', 'response_time')
    op.drop_column('messages', 'tokens_used')
    op.drop_column('messages', 'ai_prompt')
    op.drop_column('messages', 'ai_model')
    
    # 恢复conversation_id字段
    op.drop_index('ix_messages_conversation_id', table_name='messages')
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_column('messages', 'conversation_id')
    op.add_column('messages', sa.Column('conversation_id', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100), nullable=False))
    op.create_index('ix_messages_conversation_id', 'messages', ['conversation_id'], unique=False)
