"""add_pet_management_tables

Revision ID: 28b9d0f97be1
Revises: 
Create Date: 2025-06-25 17:12:16.819012

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '28b9d0f97be1'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_feeds_id'), table_name='feeds')
    op.drop_table('feeds')
    op.drop_index(op.f('ix_pet_photos_id'), table_name='pet_photos')
    op.drop_table('pet_photos')
    op.drop_index(op.f('ix_messages_conversation_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_id'), table_name='messages')
    op.drop_table('messages')
    op.drop_index(op.f('ix_pets_id'), table_name='pets')
    op.drop_table('pets')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('username', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50), nullable=False),
    sa.Column('email', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100), nullable=True),
    sa.Column('hashed_password', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255), nullable=False),
    sa.Column('display_name', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100), nullable=True),
    sa.Column('avatar_url', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255), nullable=True),
    sa.Column('is_active', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True),
    sa.Column('is_verified', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True),
    sa.Column('provider', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20), nullable=True),
    sa.Column('provider_id', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100), nullable=True),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('(now())'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_table('pets',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50), nullable=False),
    sa.Column('user_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('avatar_url', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255), nullable=True),
    sa.Column('breed', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50), nullable=True),
    sa.Column('age', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('gender', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=10), nullable=True),
    sa.Column('personality', mysql.TEXT(collation='utf8mb4_unicode_ci'), nullable=True),
    sa.Column('mood', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20), nullable=True),
    sa.Column('experience', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('level', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_active', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('(now())'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='pets_ibfk_1'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_pets_id'), 'pets', ['id'], unique=False)
    op.create_table('messages',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('conversation_id', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=100), nullable=False),
    sa.Column('user_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('pet_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('message_type', mysql.ENUM('USER', 'PET', 'SYSTEM'), nullable=False),
    sa.Column('content', mysql.TEXT(collation='utf8mb4_unicode_ci'), nullable=False),
    sa.Column('extra_data', mysql.TEXT(collation='utf8mb4_unicode_ci'), nullable=True),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('(now())'), nullable=True),
    sa.ForeignKeyConstraint(['pet_id'], ['pets.id'], name=op.f('messages_ibfk_2')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('messages_ibfk_1')),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_messages_id'), 'messages', ['id'], unique=False)
    op.create_index(op.f('ix_messages_conversation_id'), 'messages', ['conversation_id'], unique=False)
    op.create_table('pet_photos',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('pet_id', mysql.INTEGER(), autoincrement=False, nullable=False, comment='宠物ID'),
    sa.Column('url', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=500), nullable=False, comment='照片URL'),
    sa.Column('thumbnail_url', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=500), nullable=True, comment='缩略图URL'),
    sa.Column('description', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=200), nullable=True, comment='照片描述'),
    sa.Column('file_size', mysql.INTEGER(), autoincrement=False, nullable=True, comment='文件大小（字节）'),
    sa.Column('width', mysql.INTEGER(), autoincrement=False, nullable=True, comment='图片宽度'),
    sa.Column('height', mysql.INTEGER(), autoincrement=False, nullable=True, comment='图片高度'),
    sa.Column('is_avatar', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True, comment='是否为头像'),
    sa.Column('created_at', mysql.DATETIME(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['pet_id'], ['pets.id'], name=op.f('pet_photos_ibfk_1')),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_pet_photos_id'), 'pet_photos', ['id'], unique=False)
    op.create_table('feeds',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('pet_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('user_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('content', mysql.TEXT(collation='utf8mb4_unicode_ci'), nullable=False),
    sa.Column('images', mysql.JSON(), nullable=True),
    sa.Column('mood', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=20), nullable=True),
    sa.Column('tags', mysql.JSON(), nullable=True),
    sa.Column('likes_count', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('comments_count', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('shares_count', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_public', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('(now())'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['pet_id'], ['pets.id'], name=op.f('feeds_ibfk_1')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('feeds_ibfk_2')),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_feeds_id'), 'feeds', ['id'], unique=False)
    # ### end Alembic commands ### 