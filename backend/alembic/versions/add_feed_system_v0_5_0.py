"""add_feed_system_v0_5_0

Revision ID: add_feed_system_v0_5_0
Revises: add_ai_conversation_system
Create Date: 2025-06-25 21:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'add_feed_system_v0_5_0'
down_revision = 'add_ai_conversation_system'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 更新现有的feeds表结构
    # 1. 添加新字段
    op.add_column('feeds', sa.Column('location', sa.String(length=200), nullable=True, comment='位置信息'))
    op.add_column('feeds', sa.Column('views_count', sa.Integer(), nullable=True, default=0, comment='浏览数'))
    op.add_column('feeds', sa.Column('ai_generated_content', sa.Text(), nullable=True, comment='AI生成的文案'))
    op.add_column('feeds', sa.Column('ai_mood_score', sa.Float(), nullable=True, comment='AI情感分析得分'))
    op.add_column('feeds', sa.Column('ai_quality_score', sa.Float(), nullable=True, comment='AI内容质量评分'))
    op.add_column('feeds', sa.Column('ai_tags', sa.JSON(), nullable=True, comment='AI自动识别的标签'))
    op.add_column('feeds', sa.Column('status', sa.String(length=20), nullable=True, default='published', comment='动态状态'))
    op.add_column('feeds', sa.Column('is_featured', sa.Boolean(), nullable=True, default=False, comment='是否精选'))
    op.add_column('feeds', sa.Column('is_ai_generated', sa.Boolean(), nullable=True, default=False, comment='是否AI生成'))
    op.add_column('feeds', sa.Column('published_at', sa.DateTime(timezone=True), nullable=True, comment='发布时间'))
    
    # 2. 修改现有字段
    op.alter_column('feeds', 'mood', 
                   existing_type=sa.String(20),
                   type_=sa.String(50),
                   comment='心情标签')
    
    # 3. 设置默认值
    op.execute("UPDATE feeds SET views_count = 0 WHERE views_count IS NULL")
    op.execute("UPDATE feeds SET status = 'published' WHERE status IS NULL")
    op.execute("UPDATE feeds SET is_featured = FALSE WHERE is_featured IS NULL")
    op.execute("UPDATE feeds SET is_ai_generated = FALSE WHERE is_ai_generated IS NULL")
    
    # 4. 添加索引
    op.create_index('idx_feed_user_created', 'feeds', ['user_id', 'created_at'])
    op.create_index('idx_feed_pet_created', 'feeds', ['pet_id', 'created_at'])
    op.create_index('idx_feed_status_public', 'feeds', ['status', 'is_public'])
    op.create_index('idx_feed_featured', 'feeds', ['is_featured', 'created_at'])
    
    # 创建feed_likes表
    op.create_table('feed_likes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('feed_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='点赞时间'),
        sa.ForeignKeyConstraint(['feed_id'], ['feeds.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_feed_like_unique', 'feed_likes', ['feed_id', 'user_id'], unique=True)
    op.create_index('idx_feed_like_user', 'feed_likes', ['user_id', 'created_at'])
    op.create_index(op.f('ix_feed_likes_id'), 'feed_likes', ['id'], unique=False)
    op.create_index(op.f('ix_feed_likes_feed_id'), 'feed_likes', ['feed_id'], unique=False)
    op.create_index(op.f('ix_feed_likes_user_id'), 'feed_likes', ['user_id'], unique=False)
    
    # 创建feed_comments表
    op.create_table('feed_comments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('feed_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('parent_id', sa.Integer(), nullable=True),
        sa.Column('content', sa.Text(), nullable=False, comment='评论内容'),
        sa.Column('is_deleted', sa.Boolean(), nullable=True, default=False, comment='是否已删除'),
        sa.Column('is_hidden', sa.Boolean(), nullable=True, default=False, comment='是否隐藏'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='评论时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
        sa.ForeignKeyConstraint(['feed_id'], ['feeds.id'], ),
        sa.ForeignKeyConstraint(['parent_id'], ['feed_comments.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_feed_comment_feed', 'feed_comments', ['feed_id', 'created_at'])
    op.create_index('idx_feed_comment_user', 'feed_comments', ['user_id', 'created_at'])
    op.create_index('idx_feed_comment_parent', 'feed_comments', ['parent_id', 'created_at'])
    op.create_index(op.f('ix_feed_comments_id'), 'feed_comments', ['id'], unique=False)
    op.create_index(op.f('ix_feed_comments_feed_id'), 'feed_comments', ['feed_id'], unique=False)
    op.create_index(op.f('ix_feed_comments_user_id'), 'feed_comments', ['user_id'], unique=False)
    op.create_index(op.f('ix_feed_comments_parent_id'), 'feed_comments', ['parent_id'], unique=False)


def downgrade() -> None:
    # 删除feed_comments表
    op.drop_index(op.f('ix_feed_comments_parent_id'), table_name='feed_comments')
    op.drop_index(op.f('ix_feed_comments_user_id'), table_name='feed_comments')
    op.drop_index(op.f('ix_feed_comments_feed_id'), table_name='feed_comments')
    op.drop_index(op.f('ix_feed_comments_id'), table_name='feed_comments')
    op.drop_index('idx_feed_comment_parent', table_name='feed_comments')
    op.drop_index('idx_feed_comment_user', table_name='feed_comments')
    op.drop_index('idx_feed_comment_feed', table_name='feed_comments')
    op.drop_table('feed_comments')
    
    # 删除feed_likes表
    op.drop_index(op.f('ix_feed_likes_user_id'), table_name='feed_likes')
    op.drop_index(op.f('ix_feed_likes_feed_id'), table_name='feed_likes')
    op.drop_index(op.f('ix_feed_likes_id'), table_name='feed_likes')
    op.drop_index('idx_feed_like_user', table_name='feed_likes')
    op.drop_index('idx_feed_like_unique', table_name='feed_likes')
    op.drop_table('feed_likes')
    
    # 删除feeds表的索引
    op.drop_index('idx_feed_featured', table_name='feeds')
    op.drop_index('idx_feed_status_public', table_name='feeds')
    op.drop_index('idx_feed_pet_created', table_name='feeds')
    op.drop_index('idx_feed_user_created', table_name='feeds')
    
    # 删除feeds表的新字段
    op.drop_column('feeds', 'published_at')
    op.drop_column('feeds', 'is_ai_generated')
    op.drop_column('feeds', 'is_featured')
    op.drop_column('feeds', 'status')
    op.drop_column('feeds', 'ai_tags')
    op.drop_column('feeds', 'ai_quality_score')
    op.drop_column('feeds', 'ai_mood_score')
    op.drop_column('feeds', 'ai_generated_content')
    op.drop_column('feeds', 'views_count')
    op.drop_column('feeds', 'location')
    
    # 恢复mood字段类型
    op.alter_column('feeds', 'mood', 
                   existing_type=sa.String(50),
                   type_=sa.String(20))
