#!/usr/bin/env python3
"""
毛孩子AI项目 - iOS调试专用日志监控器
专门为iOS应用调试设计的简洁日志监控工具
"""

import time
import os
from pathlib import Path
from datetime import datetime
import threading
import sys


class iOSDebugMonitor:
    """iOS调试专用日志监控器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = Path(log_dir)
        self.running = False
        self.last_positions = {}
        
    def start_monitoring(self):
        """开始监控"""
        self.running = True
        
        # 清屏并显示标题
        os.system('clear' if os.name == 'posix' else 'cls')
        print("🍎 毛孩子AI - iOS调试日志监控器")
        print("=" * 60)
        print(f"📍 后端服务: http://localhost:3001")
        print(f"📚 API文档: http://localhost:3001/docs")
        print(f"🕒 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print("🔍 监控中... (按 Ctrl+C 停止)")
        print()
        
        # 初始化文件位置
        self._initialize_file_positions()
        
        try:
            while self.running:
                self._check_log_files()
                time.sleep(0.5)  # 更频繁的检查，适合调试
        except KeyboardInterrupt:
            print("\n\n⏹️  监控已停止")
            self.running = False
    
    def _initialize_file_positions(self):
        """初始化文件读取位置"""
        log_files = ["app.log", "error.log"]
        
        for filename in log_files:
            filepath = self.log_dir / filename
            if filepath.exists():
                with open(filepath, 'r', encoding='utf-8') as f:
                    f.seek(0, 2)  # 移动到文件末尾
                    self.last_positions[filename] = f.tell()
            else:
                self.last_positions[filename] = 0
    
    def _check_log_files(self):
        """检查日志文件变化"""
        log_files = ["app.log", "error.log"]
        
        for filename in log_files:
            filepath = self.log_dir / filename
            if filepath.exists():
                self._read_new_lines(filepath, filename)
    
    def _read_new_lines(self, filepath, filename):
        """读取文件中的新行"""
        try:
            current_size = filepath.stat().st_size
            last_position = self.last_positions.get(filename, 0)
            
            if current_size > last_position:
                with open(filepath, 'r', encoding='utf-8') as f:
                    f.seek(last_position)
                    new_lines = f.readlines()
                    self.last_positions[filename] = f.tell()
                    
                    for line in new_lines:
                        line = line.strip()
                        if line:
                            self._process_new_log_line(line, filename)
            
        except Exception as e:
            print(f"❌ 读取文件 {filename} 时出错: {e}")
    
    def _process_new_log_line(self, line, filename):
        """处理新的日志行"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 过滤掉健康检查的噪音
        if "GET /api/health" in line or "SELECT 1" in line:
            return
        
        # 根据日志内容分类显示
        if " | ERROR " in line:
            print(f"🔴 [{timestamp}] {line}")
        elif " | WARNING " in line:
            print(f"🟡 [{timestamp}] {line}")
        elif any(keyword in line for keyword in [
            "POST", "PUT", "DELETE", "GET /api/auth", "GET /api/pets", 
            "GET /api/feeds", "GET /api/ai", "GET /api/users"
        ]):
            # API请求
            print(f"🌐 [{timestamp}] {line}")
        elif any(keyword in line for keyword in [
            "启动", "关闭", "初始化", "连接", "登录", "注册", "上传"
        ]):
            # 重要事件
            print(f"⭐ [{timestamp}] {line}")
        elif "数据库" in line and "ERROR" not in line:
            # 数据库操作
            print(f"🗄️  [{timestamp}] {line}")
        elif filename == "error.log":
            # 错误日志文件中的所有内容
            print(f"❌ [{timestamp}] {line}")
        else:
            # 其他信息
            print(f"ℹ️  [{timestamp}] {line}")
        
        # 自动滚动，保持最新日志可见
        sys.stdout.flush()


def main():
    """主函数"""
    print("🍎 启动iOS调试日志监控器...")
    
    # 检查后端服务是否运行
    import subprocess
    try:
        result = subprocess.run(
            ["curl", "-s", "http://localhost:3001/api/health"],
            capture_output=True,
            timeout=3
        )
        if result.returncode != 0:
            print("⚠️  警告: 后端服务可能未运行")
            print("请确保后端服务已启动: cd backend && python start_dev_server.py")
            print()
    except:
        print("⚠️  无法检查后端服务状态")
        print()
    
    monitor = iOSDebugMonitor()
    monitor.start_monitoring()


if __name__ == "__main__":
    main()
