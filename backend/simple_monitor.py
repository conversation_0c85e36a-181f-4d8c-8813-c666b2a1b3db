#!/usr/bin/env python3
"""
毛孩子AI项目 - 简化版iOS调试监控器
显示后端服务的关键信息，适合iOS调试使用
"""

import subprocess
import time
import os
from datetime import datetime


def check_backend_status():
    """检查后端服务状态"""
    try:
        result = subprocess.run(
            ["curl", "-s", "http://localhost:3001/api/health"],
            capture_output=True,
            timeout=3
        )
        if result.returncode == 0:
            return "🟢 运行中"
        else:
            return "🔴 未响应"
    except:
        return "🔴 未运行"


def show_recent_logs():
    """显示最近的日志"""
    try:
        # 显示应用日志最后5行
        result = subprocess.run(
            ["tail", "-5", "logs/app.log"],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("📋 最近的应用日志:")
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print(f"  {line}")
        
        # 检查错误日志
        if os.path.exists("logs/error.log"):
            result = subprocess.run(
                ["tail", "-3", "logs/error.log"],
                capture_output=True,
                text=True
            )
            if result.returncode == 0 and result.stdout.strip():
                print("\n❌ 最近的错误:")
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        print(f"  {line}")
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")


def show_api_endpoints():
    """显示可用的API端点"""
    endpoints = [
        "GET  /api/health          - 健康检查",
        "POST /api/auth/register   - 用户注册",
        "POST /api/auth/login      - 用户登录",
        "GET  /api/feeds/          - 获取动态列表",
        "POST /api/feeds/          - 发布动态",
        "GET  /api/pets/           - 获取宠物列表",
        "POST /api/pets/           - 添加宠物",
        "POST /api/ai/chat         - AI聊天",
        "GET  /api/users/profile   - 用户资料"
    ]
    
    print("🌐 可用的API端点:")
    for endpoint in endpoints:
        print(f"  {endpoint}")


def main():
    """主函数"""
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print("🍎 毛孩子AI - iOS调试信息面板")
    print("=" * 50)
    print(f"🕒 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查服务状态
    status = check_backend_status()
    print(f"🚀 后端服务: {status}")
    print(f"📍 服务地址: http://localhost:3001")
    print(f"📚 API文档: http://localhost:3001/docs")
    
    print("\n" + "=" * 50)
    
    # 显示API端点
    show_api_endpoints()
    
    print("\n" + "=" * 50)
    
    # 显示最近日志
    show_recent_logs()
    
    print("\n" + "=" * 50)
    print("💡 调试提示:")
    print("  • 使用 curl 测试API: curl http://localhost:3001/api/health")
    print("  • 查看实时日志: python3 view_logs.py tail app.log")
    print("  • 分析日志健康: python3 analyze_logs.py")
    print("  • 重新运行此脚本: python3 simple_monitor.py")


if __name__ == "__main__":
    main()
