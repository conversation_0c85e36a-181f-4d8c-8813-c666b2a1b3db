2025-06-30 23:48:17 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:48:17 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:48:17 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:48:17 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:48:17 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:48:17 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:48:17 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:48:31 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:48:31 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:48:31 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:48:31 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:48:31 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:48:31 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:48:31 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:50:26 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-06-30 23:51:33 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:51:33 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:51:33 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:51:34 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:51:34 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:51:34 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:51:34 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:51:34 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:51:34 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:51:34 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:51:51 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:51:51 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:51:51 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:51:51 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:51:51 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:51:51 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:51:51 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:53:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-06-30 23:55:06 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:55:06 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:55:06 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:55:07 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:55:07 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:55:07 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:55:07 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:55:07 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:55:07 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:55:07 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:56:03 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:56:03 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:56:03 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:56:05 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:56:05 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:56:05 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:56:05 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:56:05 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:56:05 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:56:05 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-06-30 23:56:42 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-06-30 23:56:42 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-06-30 23:56:42 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-06-30 23:56:43 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-06-30 23:56:43 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-06-30 23:56:43 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-06-30 23:56:44 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-06-30 23:56:44 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-06-30 23:56:44 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-06-30 23:56:44 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:00:19 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:00:19 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:00:19 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:00:20 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:00:20 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:00:20 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:00:20 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:00:20 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:00:20 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:00:20 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:06:27 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:06:27 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:06:27 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:06:28 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:06:28 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:06:28 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:06:29 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:06:29 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:06:29 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:06:29 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:08:40 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:10:31 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:10:31 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:10:31 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:10:44 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:10:44 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:10:44 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:10:44 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:10:44 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:10:44 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:10:44 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:11:05 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:11:05 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:11:05 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:11:24 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:11:24 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:11:24 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:11:24 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:11:24 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:11:24 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:11:24 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:11:43 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 00:12:03 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: 无效的认证凭据
2025-07-01 00:13:11 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败
2025-07-01 00:13:59 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:13:59 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:13:59 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:14:01 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:14:01 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:14:01 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:14:01 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:14:01 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:14:01 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:14:01 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:15:06 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:15:06 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:15:06 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:15:07 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:15:07 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:15:07 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:15:07 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:15:07 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:15:07 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:15:07 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:17:33 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败
2025-07-01 00:18:28 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:18:28 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:18:28 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:18:30 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:18:30 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:18:30 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:18:30 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:18:30 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:18:30 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:18:30 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:20:58 | ERROR    | app.api.v1.feeds:create_feed:92 | 创建动态失败: cannot access local variable 'select' where it is not associated with a value
2025-07-01 00:20:58 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 创建动态失败: cannot access local variable 'select' where it is not associated with a value
2025-07-01 00:21:30 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:21:30 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:21:30 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:21:31 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:21:31 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:21:31 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:21:31 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:21:31 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:21:31 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:21:31 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:21:42 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:21:42 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:21:42 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:21:43 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:21:43 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:21:43 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:21:43 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:21:43 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:21:43 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:21:43 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:22:18 | ERROR    | app.api.v1.feeds:get_feeds:178 | 获取动态列表失败: 'AsyncSession' object has no attribute 'query'
2025-07-01 00:22:18 | ERROR    | app.api.v1.feeds:get_feeds:179 | 错误详情: Traceback (most recent call last):
  File "/Users/<USER>/WorkSpace/furryKids/backend/app/api/v1/feeds.py", line 133, in get_feeds
    is_liked = feed_service.check_user_liked(feed.id, current_user.id)
  File "/Users/<USER>/WorkSpace/furryKids/backend/app/services/feed_service.py", line 233, in check_user_liked
    like = self.db.query(FeedLike).filter(
           ^^^^^^^^^^^^^
AttributeError: 'AsyncSession' object has no attribute 'query'

2025-07-01 00:22:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 500: 获取动态列表失败: 'AsyncSession' object has no attribute 'query'
2025-07-01 00:24:49 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:24:49 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:24:49 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:24:51 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:24:51 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:24:51 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:24:51 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:24:51 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:24:51 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:24:51 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:25:01 | INFO     | app.main:lifespan:63 | 🛑 正在关闭服务...
2025-07-01 00:25:01 | INFO     | app.core.database:close_db:86 | ✅ 数据库连接已关闭
2025-07-01 00:25:01 | INFO     | app.main:lifespan:65 | ✅ 服务已关闭
2025-07-01 00:25:02 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:25:02 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:25:02 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:25:02 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:25:02 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:25:02 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:25:02 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:27:34 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:44:59 | INFO     | app.core.logging:setup_logging:64 | 日志系统初始化完成
2025-07-01 00:44:59 | INFO     | app.main:lifespan:37 | 🚀 启动毛孩子AI后端服务...
2025-07-01 00:44:59 | INFO     | app.main:lifespan:42 | ✅ 上传目录创建完成
2025-07-01 00:44:59 | INFO     | app.core.database:init_db:73 | ✅ 数据库表创建成功
2025-07-01 00:44:59 | INFO     | app.main:lifespan:46 | ✅ 数据库初始化完成
2025-07-01 00:44:59 | INFO     | app.main:lifespan:50 | ✅ 数据库连接正常
2025-07-01 00:44:59 | INFO     | app.main:lifespan:54 | 🌟 服务启动成功 - 毛孩子AI后端服务 v0.1.0
2025-07-01 00:45:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:48:15 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 401: {'success': False, 'message': '密码错误'}
2025-07-01 00:49:14 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 400: {'success': False, 'message': '用户名已存在'}
2025-07-01 00:52:19 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 00:58:04 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 08:45:39 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 08:47:53 | WARNING  | app.middleware.response_optimization:dispatch:61 | 慢请求检测: GET /api/health 耗时 2.30s
2025-07-01 08:47:53 | WARNING  | app.main:add_process_time_header:111 | 🐌 慢请求警告 - GET /api/health 耗时: 2.44s
2025-07-01 09:39:49 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:43:10 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:46:59 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:56:43 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:57:18 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:57:26 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 09:58:35 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:23:06 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:23:45 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
2025-07-01 10:24:39 | ERROR    | app.core.database:get_db:54 | 数据库会话错误: 403: Not authenticated
