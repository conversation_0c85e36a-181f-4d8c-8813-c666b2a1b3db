#!/usr/bin/env python3
"""
毛孩子AI项目实时日志监控工具
实时监控日志文件变化，提供即时的系统状态信息
"""

import time
import os
from pathlib import Path
from datetime import datetime
import threading
from collections import deque


class LogMonitor:
    """实时日志监控器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = Path(log_dir)
        self.running = False
        self.recent_logs = deque(maxlen=20)  # 保存最近20条日志
        self.error_count = 0
        self.warning_count = 0
        self.last_positions = {}  # 记录每个文件的最后读取位置
        
    def start_monitoring(self):
        """开始监控"""
        self.running = True
        print("🔍 开始实时监控毛孩子AI项目日志...")
        print("=" * 50)
        print("按 Ctrl+C 停止监控")
        print()
        
        # 初始化文件位置
        self._initialize_file_positions()
        
        try:
            while self.running:
                self._check_log_files()
                time.sleep(1)  # 每秒检查一次
        except KeyboardInterrupt:
            print("\n\n⏹️  监控已停止")
            self.running = False
    
    def _initialize_file_positions(self):
        """初始化文件读取位置"""
        log_files = ["app.log", "error.log"]
        
        for filename in log_files:
            filepath = self.log_dir / filename
            if filepath.exists():
                # 移动到文件末尾
                with open(filepath, 'r', encoding='utf-8') as f:
                    f.seek(0, 2)  # 移动到文件末尾
                    self.last_positions[filename] = f.tell()
            else:
                self.last_positions[filename] = 0
    
    def _check_log_files(self):
        """检查日志文件变化"""
        log_files = ["app.log", "error.log"]
        
        for filename in log_files:
            filepath = self.log_dir / filename
            if filepath.exists():
                self._read_new_lines(filepath, filename)
    
    def _read_new_lines(self, filepath, filename):
        """读取文件中的新行"""
        try:
            current_size = filepath.stat().st_size
            last_position = self.last_positions.get(filename, 0)
            
            if current_size > last_position:
                with open(filepath, 'r', encoding='utf-8') as f:
                    f.seek(last_position)
                    new_lines = f.readlines()
                    self.last_positions[filename] = f.tell()
                    
                    for line in new_lines:
                        line = line.strip()
                        if line:
                            self._process_new_log_line(line, filename)
            
        except Exception as e:
            print(f"❌ 读取文件 {filename} 时出错: {e}")
    
    def _process_new_log_line(self, line, filename):
        """处理新的日志行"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 添加到最近日志
        self.recent_logs.append((timestamp, filename, line))
        
        # 分析日志级别和内容
        if " | ERROR " in line:
            self.error_count += 1
            print(f"🔴 [{timestamp}] ERROR: {line}")
            self._alert_on_critical_error(line)
        elif " | WARNING " in line:
            self.warning_count += 1
            print(f"🟡 [{timestamp}] WARNING: {line}")
        elif " | INFO " in line:
            # 只显示重要的INFO日志
            if any(keyword in line for keyword in [
                "启动", "关闭", "初始化", "失败", "成功", "错误", "慢请求"
            ]):
                print(f"🔵 [{timestamp}] INFO: {line}")
        
        # 检查特殊事件
        self._check_special_events(line)
    
    def _alert_on_critical_error(self, line):
        """对关键错误发出警报"""
        critical_keywords = [
            "数据库连接失败", "服务启动失败", "内存不足", "磁盘空间不足"
        ]
        
        for keyword in critical_keywords:
            if keyword in line:
                print(f"🚨 CRITICAL ALERT: 检测到关键错误 - {keyword}")
                break
    
    def _check_special_events(self, line):
        """检查特殊事件"""
        if "启动毛孩子AI后端服务" in line:
            print(f"🚀 服务重启检测")
        elif "数据库连接正常" in line:
            print(f"✅ 数据库连接健康")
        elif "慢请求" in line:
            print(f"🐌 性能警告: 检测到慢请求")
    
    def show_status(self):
        """显示当前状态"""
        print("\n" + "="*50)
        print("📊 实时监控状态")
        print("="*50)
        print(f"🔴 错误计数: {self.error_count}")
        print(f"🟡 警告计数: {self.warning_count}")
        print(f"📝 最近日志条数: {len(self.recent_logs)}")
        
        if self.recent_logs:
            print("\n🕒 最近5条日志:")
            for timestamp, filename, line in list(self.recent_logs)[-5:]:
                print(f"  [{timestamp}] {filename}: {line[:80]}...")
        
        print("\n按 Ctrl+C 停止监控")


class InteractiveMonitor:
    """交互式监控器"""
    
    def __init__(self):
        self.monitor = LogMonitor()
        self.status_thread = None
    
    def start(self):
        """启动交互式监控"""
        print("🎛️  毛孩子AI项目日志监控器")
        print("=" * 50)
        print("命令:")
        print("  start  - 开始监控")
        print("  status - 显示状态")
        print("  recent - 显示最近日志")
        print("  clear  - 清屏")
        print("  quit   - 退出")
        print()
        
        while True:
            try:
                command = input("monitor> ").strip().lower()
                
                if command == "start":
                    if not self.monitor.running:
                        # 在新线程中启动监控
                        self.status_thread = threading.Thread(
                            target=self.monitor.start_monitoring
                        )
                        self.status_thread.daemon = True
                        self.status_thread.start()
                    else:
                        print("监控已在运行中")
                
                elif command == "status":
                    self.monitor.show_status()
                
                elif command == "recent":
                    self._show_recent_logs()
                
                elif command == "clear":
                    os.system('clear' if os.name == 'posix' else 'cls')
                
                elif command == "quit":
                    self.monitor.running = False
                    print("👋 再见！")
                    break
                
                elif command == "help":
                    print("可用命令: start, status, recent, clear, quit")
                
                else:
                    print("未知命令，输入 'help' 查看帮助")
            
            except KeyboardInterrupt:
                self.monitor.running = False
                print("\n👋 监控已停止")
                break
            except EOFError:
                break
    
    def _show_recent_logs(self):
        """显示最近的日志"""
        if not self.monitor.recent_logs:
            print("暂无日志记录")
            return
        
        print("\n📋 最近的日志记录:")
        print("-" * 50)
        for timestamp, filename, line in self.monitor.recent_logs:
            print(f"[{timestamp}] {filename}: {line}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        # 交互式模式
        interactive = InteractiveMonitor()
        interactive.start()
    else:
        # 直接监控模式
        monitor = LogMonitor()
        monitor.start_monitoring()


if __name__ == "__main__":
    main()
