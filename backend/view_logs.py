#!/usr/bin/env python3
"""
毛孩子AI项目日志查看工具
快速查看和搜索项目日志
"""

import sys
import re
from pathlib import Path
from datetime import datetime


class LogViewer:
    """日志查看器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = Path(log_dir)
    
    def list_log_files(self):
        """列出所有日志文件"""
        print("📁 可用的日志文件:")
        print("-" * 30)
        
        if not self.log_dir.exists():
            print("❌ 日志目录不存在")
            return []
        
        log_files = []
        for file in self.log_dir.glob("*.log"):
            size = file.stat().st_size
            modified = datetime.fromtimestamp(file.stat().st_mtime)
            print(f"📄 {file.name} ({size} bytes, 修改于 {modified.strftime('%Y-%m-%d %H:%M:%S')})")
            log_files.append(file.name)
        
        if not log_files:
            print("❌ 没有找到日志文件")
        
        return log_files
    
    def view_file(self, filename, lines=50, level_filter=None, search_term=None):
        """查看日志文件"""
        filepath = self.log_dir / filename
        
        if not filepath.exists():
            print(f"❌ 文件 {filename} 不存在")
            return
        
        print(f"📖 查看日志文件: {filename}")
        if level_filter:
            print(f"🔍 过滤级别: {level_filter}")
        if search_term:
            print(f"🔍 搜索关键词: {search_term}")
        print("-" * 50)
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
            
            # 过滤日志
            filtered_lines = []
            for line in all_lines:
                line = line.strip()
                if not line:
                    continue
                
                # 级别过滤
                if level_filter and f" | {level_filter.upper()} " not in line:
                    continue
                
                # 关键词搜索
                if search_term and search_term.lower() not in line.lower():
                    continue
                
                filtered_lines.append(line)
            
            # 显示最后N行
            display_lines = filtered_lines[-lines:] if lines > 0 else filtered_lines
            
            for line in display_lines:
                # 高亮显示不同级别
                if " | ERROR " in line:
                    print(f"🔴 {line}")
                elif " | WARNING " in line:
                    print(f"🟡 {line}")
                elif " | INFO " in line:
                    print(f"🔵 {line}")
                else:
                    print(f"⚪ {line}")
            
            print(f"\n📊 显示了 {len(display_lines)} 行 (共 {len(filtered_lines)} 行匹配)")
        
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    
    def search_logs(self, search_term, context_lines=2):
        """在所有日志文件中搜索"""
        print(f"🔍 在所有日志文件中搜索: '{search_term}'")
        print("-" * 50)
        
        found_any = False
        
        for log_file in self.log_dir.glob("*.log"):
            matches = self._search_in_file(log_file, search_term, context_lines)
            if matches:
                found_any = True
                print(f"\n📄 在 {log_file.name} 中找到 {len(matches)} 个匹配:")
                for match in matches:
                    print(f"  {match}")
        
        if not found_any:
            print("❌ 没有找到匹配的内容")
    
    def _search_in_file(self, filepath, search_term, context_lines):
        """在单个文件中搜索"""
        matches = []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines):
                if search_term.lower() in line.lower():
                    # 获取上下文
                    start = max(0, i - context_lines)
                    end = min(len(lines), i + context_lines + 1)
                    
                    context = []
                    for j in range(start, end):
                        prefix = ">>> " if j == i else "    "
                        context.append(f"{prefix}{lines[j].strip()}")
                    
                    matches.append("\n".join(context))
        
        except Exception as e:
            print(f"❌ 搜索文件 {filepath.name} 失败: {e}")
        
        return matches
    
    def tail_log(self, filename, lines=10):
        """实时跟踪日志文件（类似tail -f）"""
        filepath = self.log_dir / filename
        
        if not filepath.exists():
            print(f"❌ 文件 {filename} 不存在")
            return
        
        print(f"👁️  实时跟踪日志文件: {filename}")
        print("按 Ctrl+C 停止跟踪")
        print("-" * 50)
        
        # 先显示最后几行
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                for line in all_lines[-lines:]:
                    print(line.strip())
            
            # 记录当前位置
            last_position = filepath.stat().st_size
            
            # 持续监控
            import time
            while True:
                current_size = filepath.stat().st_size
                if current_size > last_position:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        f.seek(last_position)
                        new_lines = f.readlines()
                        for line in new_lines:
                            print(line.strip())
                        last_position = f.tell()
                
                time.sleep(1)
        
        except KeyboardInterrupt:
            print("\n⏹️  停止跟踪")
        except Exception as e:
            print(f"❌ 跟踪失败: {e}")


def main():
    """主函数"""
    viewer = LogViewer()
    
    if len(sys.argv) == 1:
        # 显示帮助
        print("📋 毛孩子AI项目日志查看工具")
        print("=" * 40)
        print("用法:")
        print("  python view_logs.py list                    # 列出所有日志文件")
        print("  python view_logs.py view <file> [lines]     # 查看日志文件")
        print("  python view_logs.py error [lines]          # 查看错误日志")
        print("  python view_logs.py app [lines]            # 查看应用日志")
        print("  python view_logs.py search <term>          # 搜索关键词")
        print("  python view_logs.py tail <file>            # 实时跟踪日志")
        print()
        print("示例:")
        print("  python view_logs.py view app.log 100")
        print("  python view_logs.py search '数据库错误'")
        print("  python view_logs.py tail app.log")
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        viewer.list_log_files()
    
    elif command == "view":
        if len(sys.argv) < 3:
            print("❌ 请指定要查看的文件名")
            return
        
        filename = sys.argv[2]
        lines = int(sys.argv[3]) if len(sys.argv) > 3 else 50
        viewer.view_file(filename, lines)
    
    elif command == "error":
        lines = int(sys.argv[2]) if len(sys.argv) > 2 else 50
        viewer.view_file("error.log", lines)
    
    elif command == "app":
        lines = int(sys.argv[2]) if len(sys.argv) > 2 else 50
        viewer.view_file("app.log", lines)
    
    elif command == "search":
        if len(sys.argv) < 3:
            print("❌ 请指定搜索关键词")
            return
        
        search_term = sys.argv[2]
        viewer.search_logs(search_term)
    
    elif command == "tail":
        if len(sys.argv) < 3:
            print("❌ 请指定要跟踪的文件名")
            return
        
        filename = sys.argv[2]
        viewer.tail_log(filename)
    
    else:
        print(f"❌ 未知命令: {command}")


if __name__ == "__main__":
    main()
