# 应用配置
APP_NAME=毛孩子AI后端服务
VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# 服务器配置
HOST=0.0.0.0
PORT=3001

# 数据库配置
DATABASE_URL=mysql+aiomysql://furry_user:furry_password@localhost:3306/furry_kids

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# OpenRouter AI配置
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Redis配置（可选，用于缓存）
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp

# 日志配置
LOG_FILE=logs/app.log
ERROR_LOG_FILE=logs/error.log

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
ALLOWED_CREDENTIALS=true

# 限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10 