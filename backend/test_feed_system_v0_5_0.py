#!/usr/bin/env python3
"""
FurryKids V0.5.0 动态分享系统测试
测试动态分享系统的核心功能
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List

# 测试配置
TEST_CONFIG = {
    "base_url": "http://localhost:3001",
    "test_user_id": 1,
    "test_pet_id": 1,
    "auth_token": "1"  # 简化的认证token（实际应该是JWT）
}

class FeedSystemTester:
    """动态分享系统测试器"""
    
    def __init__(self):
        self.base_url = TEST_CONFIG["base_url"]
        self.headers = {
            "Authorization": f"Bearer {TEST_CONFIG['auth_token']}",
            "Content-Type": "application/json"
        }
        self.test_results = []
        self.test_feed_ids = []  # 存储测试创建的动态ID
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        
        if data and isinstance(data, dict):
            for key, value in data.items():
                print(f"   {key}: {value}")
    
    async def test_create_feed(self) -> bool:
        """测试创建动态"""
        try:
            import requests
            
            test_data = {
                "pet_id": TEST_CONFIG["test_pet_id"],
                "content": "今天天气真好！我和主人一起去公园玩了，遇到了很多小伙伴~ 🐕🌞",
                "images": ["https://example.com/image1.jpg"],
                "mood": "开心",
                "tags": ["户外活动", "社交"],
                "location": "中央公园",
                "is_public": True
            }
            
            response = requests.post(
                f"{self.base_url}/api/feeds/",
                json=test_data,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 201:
                data = response.json()
                self.test_feed_ids.append(data["id"])
                self.log_test(
                    "创建动态",
                    True,
                    f"成功创建动态，ID: {data['id']}",
                    {
                        "动态ID": data["id"],
                        "内容长度": len(data["content"]),
                        "点赞数": data["likes_count"],
                        "评论数": data["comments_count"]
                    }
                )
                return True
            else:
                self.log_test("创建动态", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("创建动态", False, f"异常: {str(e)}")
            return False
    
    async def test_get_feeds_list(self) -> bool:
        """测试获取动态列表"""
        try:
            import requests
            
            start_time = time.time()
            response = requests.get(
                f"{self.base_url}/api/feeds/?page=1&size=10",
                headers=self.headers,
                timeout=10
            )
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                success = response_time < 500  # 要求响应时间小于500ms
                
                self.log_test(
                    "获取动态列表",
                    success,
                    f"响应时间: {response_time:.2f}ms",
                    {
                        "响应时间": f"{response_time:.2f}ms",
                        "动态数量": len(data["feeds"]),
                        "总数": data["total"],
                        "是否有更多": data["has_more"]
                    }
                )
                return success
            else:
                self.log_test("获取动态列表", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("获取动态列表", False, f"异常: {str(e)}")
            return False
    
    async def test_like_feed(self) -> bool:
        """测试点赞功能"""
        if not self.test_feed_ids:
            self.log_test("点赞功能", False, "没有可用的测试动态")
            return False
        
        try:
            import requests
            
            feed_id = self.test_feed_ids[0]
            response = requests.post(
                f"{self.base_url}/api/feeds/{feed_id}/like",
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.log_test(
                    "点赞功能",
                    True,
                    f"点赞状态: {'已点赞' if data['is_liked'] else '已取消'}",
                    {
                        "动态ID": data["feed_id"],
                        "点赞状态": data["is_liked"],
                        "总点赞数": data["likes_count"]
                    }
                )
                return True
            else:
                self.log_test("点赞功能", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("点赞功能", False, f"异常: {str(e)}")
            return False
    
    async def test_add_comment(self) -> bool:
        """测试评论功能"""
        if not self.test_feed_ids:
            self.log_test("评论功能", False, "没有可用的测试动态")
            return False
        
        try:
            import requests
            
            feed_id = self.test_feed_ids[0]
            comment_data = {
                "content": "哇，看起来玩得很开心呢！我家的小宝贝也很喜欢去公园~"
            }
            
            response = requests.post(
                f"{self.base_url}/api/feeds/{feed_id}/comments",
                json=comment_data,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 201:
                data = response.json()
                self.log_test(
                    "评论功能",
                    True,
                    f"成功添加评论，ID: {data['id']}",
                    {
                        "评论ID": data["id"],
                        "评论内容": data["content"][:30] + "...",
                        "评论时间": data["created_at"]
                    }
                )
                return True
            else:
                self.log_test("评论功能", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("评论功能", False, f"异常: {str(e)}")
            return False
    
    async def test_ai_content_generation(self) -> bool:
        """测试AI内容生成"""
        try:
            import requests
            
            ai_request = {
                "pet_id": TEST_CONFIG["test_pet_id"],
                "mood": "开心",
                "context": "刚刚和主人一起玩了球"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/feeds/ai/generate-content",
                json=ai_request,
                headers=self.headers,
                timeout=30
            )
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                content_length = len(data["content"])
                quality_score = data["quality_score"]
                
                # 质量评估
                quality_good = quality_score >= 0.8
                length_good = 50 <= content_length <= 150
                
                success = quality_good and length_good
                
                self.log_test(
                    "AI内容生成",
                    success,
                    f"质量评分: {quality_score:.2f}, 内容长度: {content_length}",
                    {
                        "生成内容": data["content"][:50] + "...",
                        "质量评分": quality_score,
                        "内容长度": content_length,
                        "识别心情": data["mood"],
                        "推荐标签": ", ".join(data["tags"]),
                        "生成时间": f"{response_time:.2f}ms"
                    }
                )
                return success
            else:
                self.log_test("AI内容生成", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("AI内容生成", False, f"异常: {str(e)}")
            return False
    
    async def test_trending_feeds(self) -> bool:
        """测试热门动态"""
        try:
            import requests
            
            start_time = time.time()
            response = requests.get(
                f"{self.base_url}/api/feeds/trending?page=1&size=5",
                headers=self.headers,
                timeout=10
            )
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                success = response_time < 500
                
                self.log_test(
                    "热门动态",
                    success,
                    f"响应时间: {response_time:.2f}ms",
                    {
                        "响应时间": f"{response_time:.2f}ms",
                        "热门动态数": len(data["feeds"]),
                        "总数": data["total"]
                    }
                )
                return success
            else:
                self.log_test("热门动态", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("热门动态", False, f"异常: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始FurryKids V0.5.0动态分享系统测试...")
        print("=" * 60)
        
        # 测试列表
        tests = [
            ("创建动态功能", self.test_create_feed),
            ("动态列表性能", self.test_get_feeds_list),
            ("点赞互动功能", self.test_like_feed),
            ("评论互动功能", self.test_add_comment),
            ("AI内容生成", self.test_ai_content_generation),
            ("热门动态推荐", self.test_trending_feeds),
        ]
        
        # 执行测试
        for test_name, test_func in tests:
            print(f"\n📋 测试: {test_name}")
            await test_func()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("📊 测试结果统计")
        print(f"总测试数: {total_tests}")
        print(f"通过数: {passed_tests}")
        print(f"失败数: {total_tests - passed_tests}")
        print(f"通过率: {success_rate:.1f}%")
        
        # 性能指标评估
        print("\n🎯 性能指标评估")
        
        # 动态发布成功率
        create_tests = [r for r in self.test_results if "创建" in r["test_name"]]
        create_success_rate = (sum(1 for t in create_tests if t["success"]) / len(create_tests) * 100) if create_tests else 0
        print(f"动态发布成功率: {create_success_rate:.1f}% (目标: >98%)")
        
        # AI内容质量
        ai_tests = [r for r in self.test_results if "AI" in r["test_name"] and r["success"]]
        if ai_tests:
            print(f"AI文案生成质量: 通过 (目标: >80%)")
        
        # 整体评估
        if success_rate >= 90:
            print("\n🎉 V0.5.0动态分享系统测试通过！系统运行正常。")
        elif success_rate >= 70:
            print("\n⚠️  V0.5.0动态分享系统基本功能正常，但有部分问题需要修复。")
        else:
            print("\n❌ V0.5.0动态分享系统存在严重问题，需要进一步调试。")
        
        return success_rate >= 90


async def main():
    """主函数"""
    tester = FeedSystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
