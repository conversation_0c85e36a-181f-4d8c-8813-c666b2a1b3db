# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
pymysql==1.1.0
aiomysql==0.2.0
cryptography==41.0.8

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
pyjwt==2.8.0
email-validator==2.1.0

# AI和机器学习
openai==1.3.7
tiktoken==0.5.2

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 数据验证和序列化
pydantic==2.5.1
pydantic-settings==2.1.0

# 工具库
python-dotenv==1.0.0
redis==5.0.1
celery==5.3.4

# 图像处理
pillow==10.1.0
python-magic==0.4.27

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
faker==20.1.0

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# WebSocket支持
websockets==12.0

# 文件上传和处理
aiofiles==23.2.1

# 时间处理
python-dateutil==2.8.2

# 环境变量
python-decouple==3.8

# CORS支持
fastapi-cors==0.0.6

# 限流
slowapi==0.1.9

# 缓存
aiocache==0.12.2 