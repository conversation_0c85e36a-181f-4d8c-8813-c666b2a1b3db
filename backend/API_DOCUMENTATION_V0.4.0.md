# FurryKids V0.4.0 API 文档

## 🚀 AI对话系统 API

### 基础信息
- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **Content-Type**: `application/json`

---

## 🤖 AI对话接口

### 1. 与宠物聊天
**POST** `/ai/chat`

发送消息给指定宠物，获得AI生成的个性化回复。

#### 请求参数
```json
{
  "message": "你好，小智！今天天气真好呢！",
  "pet_id": 1,
  "context_data": {
    "mood": "happy",
    "recent_activities": ["散步", "吃饭"],
    "special_events": "刚刚洗了澡"
  }
}
```

#### 响应示例
```json
{
  "reply": "汪汪！主人好！今天天气确实很棒呢，我也很开心！🐕",
  "mood": "happy",
  "actions": ["摇尾巴", "跳跃"],
  "emotions": ["开心", "兴奋"],
  "confidence": 0.92,
  "conversation_id": 1,
  "message_id": 15,
  "response_time": 0.85,
  "tokens_used": 45,
  "from_cache": false
}
```

### 2. 获取对话历史
**GET** `/ai/conversations/{pet_id}/history`

获取与指定宠物的对话历史记录。

#### 查询参数
- `limit`: 限制数量 (默认: 20)
- `offset`: 偏移量 (默认: 0)

#### 响应示例
```json
{
  "conversation_id": 1,
  "messages": [
    {
      "id": 14,
      "content": "你好，小智！",
      "type": "user",
      "mood": null,
      "actions": null,
      "emotions": null,
      "created_at": "2025-06-25T12:30:00Z",
      "confidence": null,
      "tokens_used": null
    },
    {
      "id": 15,
      "content": "汪汪！主人好！",
      "type": "pet",
      "mood": "happy",
      "actions": ["摇尾巴"],
      "emotions": ["开心"],
      "created_at": "2025-06-25T12:30:01Z",
      "confidence": 0.92,
      "tokens_used": 45
    }
  ],
  "total_count": 2,
  "has_more": false
}
```

### 3. 获取对话摘要
**GET** `/ai/conversations/{pet_id}/summary`

获取与指定宠物的对话统计摘要。

#### 响应示例
```json
{
  "conversation_id": 1,
  "pet_name": "小智",
  "message_count": 24,
  "total_tokens": 1250,
  "created_at": "2025-06-25T10:00:00Z",
  "last_message_at": "2025-06-25T12:30:01Z",
  "is_active": true
}
```

### 4. 生成对话开场白
**POST** `/ai/generate-starter/{pet_id}`

为指定宠物生成个性化的对话开场白。

#### 响应示例
```json
{
  "starter": "汪汪！主人，小智想你了！今天心情很好呢～🐕",
  "pet_name": "小智",
  "pet_mood": "HAPPY"
}
```

### 5. 获取用户对话列表
**GET** `/ai/conversations`

获取当前用户的所有对话列表。

#### 查询参数
- `pet_id`: 宠物ID (可选)
- `limit`: 限制数量 (默认: 10)

#### 响应示例
```json
{
  "conversations": [
    {
      "id": 1,
      "pet_id": 1,
      "pet_name": "小智",
      "pet_avatar": "https://example.com/avatar.jpg",
      "title": "与小智的对话",
      "message_count": 24,
      "last_message_at": "2025-06-25T12:30:01Z",
      "is_active": true
    }
  ],
  "total": 1
}
```

### 6. 删除对话
**DELETE** `/ai/conversations/{conversation_id}`

删除(归档)指定的对话。

#### 响应示例
```json
{
  "message": "对话已删除",
  "conversation_id": 1
}
```

---

## 📊 性能监控接口

### 1. 获取缓存统计
**GET** `/ai/stats/cache`

获取系统缓存使用统计信息。

#### 响应示例
```json
{
  "cache_stats": {
    "hits": 150,
    "misses": 50,
    "sets": 45,
    "deletes": 5,
    "hit_rate": 75.0,
    "total_requests": 200,
    "cache_type": "memory",
    "memory_cache_size": 45
  },
  "timestamp": "2025-06-25T12:30:00Z"
}
```

### 2. 获取Token使用统计
**GET** `/ai/stats/tokens`

获取用户的AI Token使用统计。

#### 查询参数
- `days`: 统计天数 (默认: 7)

#### 响应示例
```json
{
  "usage_summary": {
    "user_id": 1,
    "period_days": 7,
    "total_input_tokens": 2500,
    "total_output_tokens": 1800,
    "total_cost": 0.025,
    "total_requests": 45,
    "daily_breakdown": [
      {
        "date": "2025-06-25",
        "input_tokens": 500,
        "output_tokens": 350,
        "requests": 12
      }
    ],
    "model_breakdown": {
      "google/gemma-2-9b-it:free": {
        "input_tokens": 2500,
        "output_tokens": 1800,
        "requests": 45
      }
    }
  },
  "timestamp": "2025-06-25T12:30:00Z"
}
```

### 3. 清除用户缓存
**DELETE** `/ai/cache/clear`

清除当前用户的所有AI响应缓存。

#### 响应示例
```json
{
  "message": "已清除 15 个缓存项",
  "cleared_count": 15,
  "user_id": 1
}
```

---

## 🔧 错误处理

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 常见错误码
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证或token无效
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `500 Internal Server Error`: 服务器内部错误

---

## 🚀 使用示例

### Python 示例
```python
import requests

# 设置认证头
headers = {
    "Authorization": "Bearer your_jwt_token",
    "Content-Type": "application/json"
}

# 发送消息给宠物
response = requests.post(
    "http://localhost:8000/api/v1/ai/chat",
    headers=headers,
    json={
        "message": "你好，小智！",
        "pet_id": 1
    }
)

result = response.json()
print(f"宠物回复: {result['reply']}")
print(f"心情: {result['mood']}")
print(f"动作: {result['actions']}")
```

### JavaScript 示例
```javascript
const token = 'your_jwt_token';

async function chatWithPet(message, petId) {
  const response = await fetch('/api/v1/ai/chat', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      message: message,
      pet_id: petId
    })
  });
  
  const result = await response.json();
  return result;
}

// 使用示例
chatWithPet("你好，小智！", 1).then(result => {
  console.log('宠物回复:', result.reply);
  console.log('心情:', result.mood);
  console.log('动作:', result.actions);
});
```

---

## 📝 更新日志

### V0.4.0 (2025-06-25)
- ✅ 新增完整的AI对话系统API
- ✅ 支持个性化宠物回复
- ✅ 添加对话历史管理
- ✅ 实现智能缓存机制
- ✅ 新增性能监控接口
- ✅ 支持Token使用统计

### 下一版本计划
- 🔄 WebSocket实时对话支持
- 🔄 语音消息支持
- 🔄 图片消息支持
- 🔄 群组对话功能

---

**FurryKids V0.4.0 - 让AI宠物更有灵魂！** 🐾✨
