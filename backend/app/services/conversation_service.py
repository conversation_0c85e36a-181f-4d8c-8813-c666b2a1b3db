"""
对话上下文管理服务
管理用户与宠物的对话历史和上下文
"""

from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, func
from sqlalchemy.orm import selectinload
from datetime import datetime, timedelta
import json

from app.models.conversation import Conversation
from app.models.message import Message, MessageType
from app.models.pet import Pet
from app.models.user import User


class ConversationService:
    """对话上下文管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_or_create_conversation(
        self, 
        user_id: int, 
        pet_id: int
    ) -> Conversation:
        """
        获取或创建对话会话
        
        Args:
            user_id: 用户ID
            pet_id: 宠物ID
            
        Returns:
            对话会话对象
        """
        # 查找现有的活跃对话
        stmt = select(Conversation).where(
            Conversation.user_id == user_id,
            Conversation.pet_id == pet_id,
            Conversation.is_active == True
        ).order_by(desc(Conversation.last_message_at))
        
        result = await self.db.execute(stmt)
        conversation = result.scalars().first()
        
        if conversation:
            return conversation
        
        # 创建新对话
        conversation = Conversation(
            user_id=user_id,
            pet_id=pet_id,
            title=f"与宠物的对话",
            is_active=True,
            context_window=10,
            message_count=0,
            total_tokens=0
        )
        
        self.db.add(conversation)
        await self.db.commit()
        await self.db.refresh(conversation)
        
        return conversation
    
    async def add_message(
        self,
        conversation_id: int,
        user_id: int,
        pet_id: int,
        content: str,
        message_type: MessageType,
        ai_data: Optional[Dict[str, Any]] = None
    ) -> Message:
        """
        添加消息到对话
        
        Args:
            conversation_id: 对话ID
            user_id: 用户ID
            pet_id: 宠物ID
            content: 消息内容
            message_type: 消息类型
            ai_data: AI相关数据
            
        Returns:
            消息对象
        """
        message = Message(
            conversation_id=conversation_id,
            user_id=user_id,
            pet_id=pet_id,
            message_type=message_type,
            content=content
        )
        
        # 添加AI相关数据
        if ai_data:
            message.ai_model = ai_data.get("ai_model")
            message.ai_prompt = ai_data.get("ai_prompt")
            message.tokens_used = ai_data.get("tokens_used")
            message.response_time = ai_data.get("response_time")
            message.confidence_score = ai_data.get("confidence")
            message.mood = ai_data.get("mood")
            message.actions = ai_data.get("actions")
            message.emotions = ai_data.get("emotions")
            message.extra_data = ai_data.get("extra_data")
        
        self.db.add(message)
        
        # 更新对话统计
        await self._update_conversation_stats(conversation_id, ai_data)
        
        await self.db.commit()
        await self.db.refresh(message)
        
        return message
    
    async def get_conversation_history(
        self,
        conversation_id: int,
        limit: int = 20,
        offset: int = 0
    ) -> List[Message]:
        """
        获取对话历史
        
        Args:
            conversation_id: 对话ID
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            消息列表
        """
        stmt = select(Message).where(
            Message.conversation_id == conversation_id
        ).order_by(desc(Message.created_at)).limit(limit).offset(offset)
        
        result = await self.db.execute(stmt)
        messages = result.scalars().all()
        
        # 返回时间正序
        return list(reversed(messages))
    
    async def get_context_messages(
        self,
        conversation_id: int,
        context_window: int = 10
    ) -> List[Dict[str, str]]:
        """
        获取上下文消息（用于AI对话）
        
        Args:
            conversation_id: 对话ID
            context_window: 上下文窗口大小
            
        Returns:
            格式化的消息列表
        """
        messages = await self.get_conversation_history(
            conversation_id, 
            limit=context_window
        )
        
        formatted_messages = []
        for message in messages:
            role = "user" if message.message_type == MessageType.USER else "assistant"
            formatted_messages.append({
                "role": role,
                "content": message.content
            })
        
        return formatted_messages
    
    async def update_conversation_context(
        self,
        conversation_id: int,
        context_data: Dict[str, Any]
    ) -> None:
        """
        更新对话上下文数据
        
        Args:
            conversation_id: 对话ID
            context_data: 上下文数据
        """
        stmt = select(Conversation).where(Conversation.id == conversation_id)
        result = await self.db.execute(stmt)
        conversation = result.scalars().first()
        
        if conversation:
            # 合并现有上下文数据
            existing_context = conversation.context_data or {}
            existing_context.update(context_data)
            
            conversation.context_data = existing_context
            conversation.updated_at = datetime.utcnow()
            
            await self.db.commit()
    
    async def get_conversation_summary(
        self,
        conversation_id: int
    ) -> Dict[str, Any]:
        """
        获取对话摘要信息
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            对话摘要
        """
        # 获取对话基本信息
        stmt = select(Conversation).options(
            selectinload(Conversation.pet),
            selectinload(Conversation.user)
        ).where(Conversation.id == conversation_id)
        
        result = await self.db.execute(stmt)
        conversation = result.scalars().first()
        
        if not conversation:
            return {}
        
        # 获取消息统计
        message_stats = await self._get_message_stats(conversation_id)
        
        # 获取最近活动
        recent_messages = await self.get_conversation_history(conversation_id, limit=5)
        
        return {
            "conversation_id": conversation_id,
            "pet_name": conversation.pet.name,
            "pet_breed": conversation.pet.breed,
            "user_name": conversation.user.username,
            "created_at": conversation.created_at,
            "last_message_at": conversation.last_message_at,
            "message_count": conversation.message_count,
            "total_tokens": conversation.total_tokens,
            "is_active": conversation.is_active,
            "context_data": conversation.context_data,
            "message_stats": message_stats,
            "recent_messages": [
                {
                    "content": msg.content,
                    "type": msg.message_type.value,
                    "created_at": msg.created_at,
                    "mood": msg.mood
                } for msg in recent_messages
            ]
        }
    
    async def archive_conversation(
        self,
        conversation_id: int,
        summary: Optional[str] = None
    ) -> None:
        """
        归档对话
        
        Args:
            conversation_id: 对话ID
            summary: 对话摘要
        """
        stmt = select(Conversation).where(Conversation.id == conversation_id)
        result = await self.db.execute(stmt)
        conversation = result.scalars().first()
        
        if conversation:
            conversation.is_active = False
            conversation.summary = summary
            conversation.updated_at = datetime.utcnow()
            
            await self.db.commit()
    
    async def get_user_conversations(
        self,
        user_id: int,
        pet_id: Optional[int] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取用户的对话列表
        
        Args:
            user_id: 用户ID
            pet_id: 宠物ID（可选）
            limit: 限制数量
            
        Returns:
            对话列表
        """
        stmt = select(Conversation).options(
            selectinload(Conversation.pet)
        ).where(Conversation.user_id == user_id)
        
        if pet_id:
            stmt = stmt.where(Conversation.pet_id == pet_id)
        
        stmt = stmt.order_by(desc(Conversation.last_message_at)).limit(limit)
        
        result = await self.db.execute(stmt)
        conversations = result.scalars().all()
        
        conversation_list = []
        for conv in conversations:
            conversation_list.append({
                "id": conv.id,
                "pet_id": conv.pet_id,
                "pet_name": conv.pet.name,
                "pet_avatar": conv.pet.avatar_url,
                "title": conv.title,
                "message_count": conv.message_count,
                "last_message_at": conv.last_message_at,
                "is_active": conv.is_active
            })
        
        return conversation_list
    
    async def _update_conversation_stats(
        self,
        conversation_id: int,
        ai_data: Optional[Dict[str, Any]] = None
    ) -> None:
        """更新对话统计信息"""
        stmt = select(Conversation).where(Conversation.id == conversation_id)
        result = await self.db.execute(stmt)
        conversation = result.scalars().first()
        
        if conversation:
            conversation.message_count += 1
            conversation.last_message_at = datetime.utcnow()
            
            if ai_data and ai_data.get("tokens_used"):
                conversation.total_tokens += ai_data["tokens_used"]
    
    async def _get_message_stats(self, conversation_id: int) -> Dict[str, Any]:
        """获取消息统计信息"""
        # 消息类型统计
        stmt = select(
            Message.message_type,
            func.count(Message.id).label("count")
        ).where(
            Message.conversation_id == conversation_id
        ).group_by(Message.message_type)
        
        result = await self.db.execute(stmt)
        type_stats = {row.message_type.value: row.count for row in result}
        
        # 心情统计
        stmt = select(
            Message.mood,
            func.count(Message.id).label("count")
        ).where(
            Message.conversation_id == conversation_id,
            Message.mood.isnot(None)
        ).group_by(Message.mood)
        
        result = await self.db.execute(stmt)
        mood_stats = {row.mood: row.count for row in result if row.mood}
        
        return {
            "message_types": type_stats,
            "mood_distribution": mood_stats
        }
