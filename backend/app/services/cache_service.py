"""
缓存服务
提供AI对话系统的缓存功能，优化性能
"""

import json
import hashlib
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from loguru import logger

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis不可用，将使用内存缓存")


class CacheService:
    """缓存服务"""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_client = None
        self.memory_cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        
        if REDIS_AVAILABLE and redis_url:
            try:
                self.redis_client = redis.from_url(redis_url)
                logger.info("Redis缓存已启用")
            except Exception as e:
                logger.warning(f"Redis连接失败，使用内存缓存: {e}")
        else:
            logger.info("使用内存缓存")
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            if self.redis_client:
                value = await self.redis_client.get(key)
                if value:
                    self.cache_stats["hits"] += 1
                    return json.loads(value)
            else:
                # 内存缓存
                cache_item = self.memory_cache.get(key)
                if cache_item and cache_item["expires_at"] > datetime.utcnow():
                    self.cache_stats["hits"] += 1
                    return cache_item["value"]
                elif cache_item:
                    # 过期删除
                    del self.memory_cache[key]
            
            self.cache_stats["misses"] += 1
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败: {e}")
            self.cache_stats["misses"] += 1
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire_seconds: int = 3600
    ) -> bool:
        """设置缓存值"""
        try:
            if self.redis_client:
                await self.redis_client.setex(
                    key, 
                    expire_seconds, 
                    json.dumps(value, ensure_ascii=False)
                )
            else:
                # 内存缓存
                self.memory_cache[key] = {
                    "value": value,
                    "expires_at": datetime.utcnow() + timedelta(seconds=expire_seconds)
                }
                
                # 清理过期缓存
                await self._cleanup_memory_cache()
            
            self.cache_stats["sets"] += 1
            return True
            
        except Exception as e:
            logger.error(f"缓存设置失败: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            if self.redis_client:
                await self.redis_client.delete(key)
            else:
                self.memory_cache.pop(key, None)
            
            self.cache_stats["deletes"] += 1
            return True
            
        except Exception as e:
            logger.error(f"缓存删除失败: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        try:
            count = 0
            if self.redis_client:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    count = await self.redis_client.delete(*keys)
            else:
                # 内存缓存模式匹配
                keys_to_delete = []
                for key in self.memory_cache.keys():
                    if pattern.replace("*", "") in key:
                        keys_to_delete.append(key)
                
                for key in keys_to_delete:
                    del self.memory_cache[key]
                    count += 1
            
            self.cache_stats["deletes"] += count
            return count
            
        except Exception as e:
            logger.error(f"批量删除缓存失败: {e}")
            return 0
    
    async def _cleanup_memory_cache(self):
        """清理过期的内存缓存"""
        if len(self.memory_cache) > 1000:  # 超过1000个缓存项时清理
            now = datetime.utcnow()
            expired_keys = [
                key for key, item in self.memory_cache.items()
                if item["expires_at"] <= now
            ]
            
            for key in expired_keys:
                del self.memory_cache[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = (self.cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            "hit_rate": round(hit_rate, 2),
            "total_requests": total_requests,
            "cache_type": "redis" if self.redis_client else "memory",
            "memory_cache_size": len(self.memory_cache) if not self.redis_client else None
        }
    
    async def close(self):
        """关闭缓存连接"""
        if self.redis_client:
            await self.redis_client.close()


class AIResponseCache:
    """AI响应缓存管理器"""
    
    def __init__(self, cache_service: CacheService):
        self.cache = cache_service
        self.default_expire = 3600  # 1小时
    
    def _generate_cache_key(
        self, 
        user_id: int, 
        pet_id: int, 
        message: str, 
        context_hash: str = ""
    ) -> str:
        """生成缓存键"""
        content = f"{user_id}:{pet_id}:{message}:{context_hash}"
        return f"ai_response:{hashlib.md5(content.encode()).hexdigest()}"
    
    def _generate_context_hash(self, context_data: Dict[str, Any]) -> str:
        """生成上下文哈希"""
        if not context_data:
            return ""
        
        # 只考虑影响AI回复的关键上下文
        key_context = {
            "mood": context_data.get("mood"),
            "recent_activities": context_data.get("recent_activities", [])[:3],  # 只取最近3个活动
            "personality_tags": context_data.get("personality_tags", [])
        }
        
        content = json.dumps(key_context, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(content.encode()).hexdigest()[:8]
    
    async def get_cached_response(
        self,
        user_id: int,
        pet_id: int,
        message: str,
        context_data: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的AI响应"""
        context_hash = self._generate_context_hash(context_data or {})
        cache_key = self._generate_cache_key(user_id, pet_id, message, context_hash)
        
        cached_response = await self.cache.get(cache_key)
        if cached_response:
            logger.info(f"AI响应缓存命中: {cache_key[:16]}...")
            # 更新时间戳
            cached_response["cached_at"] = datetime.utcnow().isoformat()
            cached_response["from_cache"] = True
        
        return cached_response
    
    async def cache_response(
        self,
        user_id: int,
        pet_id: int,
        message: str,
        response: Dict[str, Any],
        context_data: Optional[Dict[str, Any]] = None,
        expire_seconds: Optional[int] = None
    ) -> bool:
        """缓存AI响应"""
        context_hash = self._generate_context_hash(context_data or {})
        cache_key = self._generate_cache_key(user_id, pet_id, message, context_hash)
        
        # 添加缓存元数据
        cache_data = {
            **response,
            "cached_at": datetime.utcnow().isoformat(),
            "from_cache": False
        }
        
        expire_time = expire_seconds or self.default_expire
        success = await self.cache.set(cache_key, cache_data, expire_time)
        
        if success:
            logger.info(f"AI响应已缓存: {cache_key[:16]}...")
        
        return success
    
    async def clear_user_cache(self, user_id: int) -> int:
        """清除用户相关的缓存"""
        pattern = f"ai_response:*{user_id}*"
        return await self.cache.clear_pattern(pattern)
    
    async def clear_pet_cache(self, pet_id: int) -> int:
        """清除宠物相关的缓存"""
        pattern = f"ai_response:*{pet_id}*"
        return await self.cache.clear_pattern(pattern)


class TokenUsageTracker:
    """Token使用统计跟踪器"""
    
    def __init__(self, cache_service: CacheService):
        self.cache = cache_service
    
    async def record_usage(
        self,
        user_id: int,
        pet_id: int,
        model: str,
        input_tokens: int,
        output_tokens: int,
        cost: float = 0.0
    ) -> None:
        """记录Token使用"""
        timestamp = datetime.utcnow()
        date_key = timestamp.strftime("%Y-%m-%d")
        hour_key = timestamp.strftime("%Y-%m-%d-%H")
        
        # 记录每日统计
        daily_key = f"token_usage:daily:{user_id}:{date_key}"
        daily_stats = await self.cache.get(daily_key) or {
            "user_id": user_id,
            "date": date_key,
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0.0,
            "request_count": 0,
            "models": {}
        }
        
        daily_stats["total_input_tokens"] += input_tokens
        daily_stats["total_output_tokens"] += output_tokens
        daily_stats["total_cost"] += cost
        daily_stats["request_count"] += 1
        
        if model not in daily_stats["models"]:
            daily_stats["models"][model] = {
                "input_tokens": 0,
                "output_tokens": 0,
                "requests": 0
            }
        
        daily_stats["models"][model]["input_tokens"] += input_tokens
        daily_stats["models"][model]["output_tokens"] += output_tokens
        daily_stats["models"][model]["requests"] += 1
        
        await self.cache.set(daily_key, daily_stats, 86400 * 7)  # 保存7天
        
        # 记录每小时统计
        hourly_key = f"token_usage:hourly:{user_id}:{hour_key}"
        hourly_stats = await self.cache.get(hourly_key) or {
            "user_id": user_id,
            "hour": hour_key,
            "input_tokens": 0,
            "output_tokens": 0,
            "requests": 0
        }
        
        hourly_stats["input_tokens"] += input_tokens
        hourly_stats["output_tokens"] += output_tokens
        hourly_stats["requests"] += 1
        
        await self.cache.set(hourly_key, hourly_stats, 86400)  # 保存24小时
    
    async def get_daily_usage(self, user_id: int, date: str) -> Dict[str, Any]:
        """获取每日使用统计"""
        daily_key = f"token_usage:daily:{user_id}:{date}"
        return await self.cache.get(daily_key) or {}
    
    async def get_usage_summary(self, user_id: int, days: int = 7) -> Dict[str, Any]:
        """获取使用统计摘要"""
        summary = {
            "user_id": user_id,
            "period_days": days,
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_cost": 0.0,
            "total_requests": 0,
            "daily_breakdown": [],
            "model_breakdown": {}
        }
        
        for i in range(days):
            date = (datetime.utcnow() - timedelta(days=i)).strftime("%Y-%m-%d")
            daily_stats = await self.get_daily_usage(user_id, date)
            
            if daily_stats:
                summary["total_input_tokens"] += daily_stats.get("total_input_tokens", 0)
                summary["total_output_tokens"] += daily_stats.get("total_output_tokens", 0)
                summary["total_cost"] += daily_stats.get("total_cost", 0.0)
                summary["total_requests"] += daily_stats.get("request_count", 0)
                
                summary["daily_breakdown"].append({
                    "date": date,
                    "input_tokens": daily_stats.get("total_input_tokens", 0),
                    "output_tokens": daily_stats.get("total_output_tokens", 0),
                    "requests": daily_stats.get("request_count", 0)
                })
                
                # 合并模型统计
                for model, stats in daily_stats.get("models", {}).items():
                    if model not in summary["model_breakdown"]:
                        summary["model_breakdown"][model] = {
                            "input_tokens": 0,
                            "output_tokens": 0,
                            "requests": 0
                        }
                    
                    summary["model_breakdown"][model]["input_tokens"] += stats["input_tokens"]
                    summary["model_breakdown"][model]["output_tokens"] += stats["output_tokens"]
                    summary["model_breakdown"][model]["requests"] += stats["requests"]
        
        return summary


# 创建全局缓存服务实例
cache_service = CacheService()
ai_response_cache = AIResponseCache(cache_service)
token_tracker = TokenUsageTracker(cache_service)
