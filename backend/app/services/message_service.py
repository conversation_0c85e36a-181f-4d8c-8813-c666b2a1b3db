"""
消息处理服务
包含内容过滤、情感分析、消息验证等功能
"""

import re
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from loguru import logger

from app.utils.ai_client import ai_client


class MessageService:
    """消息处理服务"""
    
    # 敏感词列表（示例）
    SENSITIVE_WORDS = [
        "暴力", "色情", "政治", "赌博", "毒品", "诈骗",
        "死亡", "自杀", "伤害", "攻击", "仇恨", "歧视"
    ]
    
    # 不当内容模式
    INAPPROPRIATE_PATTERNS = [
        r'\b(?:fuck|shit|damn|hell)\b',  # 英文脏话
        r'[操草艹][你妈他她它]',  # 中文脏话
        r'[死去滚]开',  # 恶意词汇
        r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',  # URL链接
    ]
    
    def __init__(self):
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.INAPPROPRIATE_PATTERNS]
    
    async def process_user_message(self, content: str) -> Dict[str, Any]:
        """
        处理用户消息
        
        Args:
            content: 消息内容
            
        Returns:
            处理结果
        """
        result = {
            "original_content": content,
            "processed_content": content,
            "is_safe": True,
            "filter_reasons": [],
            "sentiment": None,
            "suggestions": []
        }
        
        # 1. 内容过滤
        filter_result = await self.filter_content(content)
        result.update(filter_result)
        
        # 2. 情感分析（仅对安全内容进行）
        if result["is_safe"]:
            sentiment_result = await self.analyze_sentiment(content)
            result["sentiment"] = sentiment_result
        
        # 3. 生成建议
        if not result["is_safe"]:
            result["suggestions"] = self._generate_safety_suggestions(result["filter_reasons"])
        
        return result
    
    async def filter_content(self, content: str) -> Dict[str, Any]:
        """
        内容过滤
        
        Args:
            content: 消息内容
            
        Returns:
            过滤结果
        """
        filter_reasons = []
        processed_content = content
        
        # 1. 敏感词检测
        for word in self.SENSITIVE_WORDS:
            if word in content:
                filter_reasons.append(f"包含敏感词: {word}")
                processed_content = processed_content.replace(word, "*" * len(word))
        
        # 2. 正则模式检测
        for pattern in self.compiled_patterns:
            if pattern.search(content):
                filter_reasons.append("包含不当内容")
                processed_content = pattern.sub("***", processed_content)
        
        # 3. 长度检查
        if len(content) > 1000:
            filter_reasons.append("消息过长")
        
        # 4. 空内容检查
        if not content.strip():
            filter_reasons.append("消息为空")
        
        # 5. 重复字符检查
        if self._has_excessive_repetition(content):
            filter_reasons.append("包含过多重复字符")
        
        return {
            "processed_content": processed_content,
            "is_safe": len(filter_reasons) == 0,
            "filter_reasons": filter_reasons
        }
    
    async def analyze_sentiment(self, content: str) -> Dict[str, Any]:
        """
        情感分析
        
        Args:
            content: 消息内容
            
        Returns:
            情感分析结果
        """
        try:
            # 使用AI进行情感分析
            sentiment_result = await ai_client.analyze_sentiment(content)
            
            # 添加本地规则增强
            local_analysis = self._local_sentiment_analysis(content)
            
            # 合并结果
            return {
                "ai_analysis": sentiment_result,
                "local_analysis": local_analysis,
                "final_sentiment": self._merge_sentiment_results(sentiment_result, local_analysis)
            }
            
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return self._local_sentiment_analysis(content)
    
    def _local_sentiment_analysis(self, content: str) -> Dict[str, Any]:
        """本地情感分析"""
        positive_words = ["开心", "高兴", "快乐", "喜欢", "爱", "好", "棒", "赞", "谢谢", "感谢"]
        negative_words = ["伤心", "难过", "生气", "讨厌", "恨", "坏", "糟糕", "不好", "痛苦", "失望"]
        neutral_words = ["好的", "知道", "明白", "可以", "行", "嗯", "哦", "是的"]
        
        positive_count = sum(1 for word in positive_words if word in content)
        negative_count = sum(1 for word in negative_words if word in content)
        neutral_count = sum(1 for word in neutral_words if word in content)
        
        total_count = positive_count + negative_count + neutral_count
        
        if total_count == 0:
            sentiment = "neutral"
            confidence = 0.5
        elif positive_count > negative_count:
            sentiment = "positive"
            confidence = min(0.9, 0.6 + (positive_count / max(total_count, 1)) * 0.3)
        elif negative_count > positive_count:
            sentiment = "negative"
            confidence = min(0.9, 0.6 + (negative_count / max(total_count, 1)) * 0.3)
        else:
            sentiment = "neutral"
            confidence = 0.7
        
        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "emotions": self._extract_emotions(content),
            "mood_score": self._calculate_mood_score(sentiment, confidence)
        }
    
    def _extract_emotions(self, content: str) -> List[str]:
        """提取情感标签"""
        emotion_keywords = {
            "开心": ["开心", "高兴", "快乐", "兴奋", "愉快"],
            "伤心": ["伤心", "难过", "悲伤", "失落", "沮丧"],
            "生气": ["生气", "愤怒", "恼火", "烦躁", "不爽"],
            "惊讶": ["惊讶", "震惊", "意外", "吃惊", "惊奇"],
            "害怕": ["害怕", "恐惧", "担心", "紧张", "焦虑"],
            "厌恶": ["讨厌", "恶心", "反感", "厌烦", "嫌弃"],
            "期待": ["期待", "盼望", "希望", "想要", "渴望"],
            "平静": ["平静", "安静", "放松", "淡定", "冷静"]
        }
        
        detected_emotions = []
        for emotion, keywords in emotion_keywords.items():
            if any(keyword in content for keyword in keywords):
                detected_emotions.append(emotion)
        
        return detected_emotions if detected_emotions else ["平静"]
    
    def _calculate_mood_score(self, sentiment: str, confidence: float) -> int:
        """计算心情分数 (1-10)"""
        base_scores = {
            "positive": 8,
            "neutral": 5,
            "negative": 3
        }
        
        base_score = base_scores.get(sentiment, 5)
        adjustment = (confidence - 0.5) * 2  # -1 to 1
        
        final_score = base_score + adjustment
        return max(1, min(10, int(final_score)))
    
    def _merge_sentiment_results(
        self, 
        ai_result: Dict[str, Any], 
        local_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """合并AI和本地情感分析结果"""
        # 如果AI分析置信度高，优先使用AI结果
        if ai_result.get("confidence", 0) > 0.8:
            return ai_result
        
        # 否则结合两者结果
        ai_confidence = ai_result.get("confidence", 0)
        local_confidence = local_result.get("confidence", 0)
        
        if ai_confidence > local_confidence:
            primary_result = ai_result
            secondary_result = local_result
        else:
            primary_result = local_result
            secondary_result = ai_result
        
        return {
            "sentiment": primary_result["sentiment"],
            "confidence": (ai_confidence + local_confidence) / 2,
            "emotions": list(set(primary_result.get("emotions", []) + secondary_result.get("emotions", []))),
            "mood_score": (primary_result.get("mood_score", 5) + secondary_result.get("mood_score", 5)) // 2
        }
    
    def _has_excessive_repetition(self, content: str) -> bool:
        """检查是否有过多重复字符"""
        # 检查连续重复字符
        for i in range(len(content) - 4):
            if len(set(content[i:i+5])) == 1:  # 5个连续相同字符
                return True
        
        # 检查重复模式
        for length in range(2, min(10, len(content) // 3)):
            pattern = content[:length]
            if content.count(pattern) > 3:
                return True
        
        return False
    
    def _generate_safety_suggestions(self, filter_reasons: List[str]) -> List[str]:
        """生成安全建议"""
        suggestions = []
        
        for reason in filter_reasons:
            if "敏感词" in reason:
                suggestions.append("请使用更友善的语言与宠物交流")
            elif "不当内容" in reason:
                suggestions.append("让我们保持对话的纯净和美好")
            elif "过长" in reason:
                suggestions.append("消息太长了，试试简短一些的表达")
            elif "为空" in reason:
                suggestions.append("请输入一些内容来和宠物聊天")
            elif "重复字符" in reason:
                suggestions.append("避免使用过多重复的字符")
        
        if not suggestions:
            suggestions.append("让我们用更好的方式与宠物交流吧")
        
        return suggestions
    
    async def validate_message_context(
        self, 
        content: str, 
        conversation_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        验证消息上下文
        
        Args:
            content: 当前消息内容
            conversation_history: 对话历史
            
        Returns:
            验证结果
        """
        result = {
            "is_valid": True,
            "warnings": [],
            "context_score": 1.0
        }
        
        # 检查是否与上下文相关
        if len(conversation_history) > 0:
            last_message = conversation_history[-1].get("content", "")
            similarity = self._calculate_similarity(content, last_message)
            
            if similarity < 0.1:
                result["warnings"].append("消息与上下文关联度较低")
                result["context_score"] *= 0.8
        
        # 检查对话频率
        recent_messages = conversation_history[-5:] if len(conversation_history) >= 5 else conversation_history
        user_messages = [msg for msg in recent_messages if msg.get("type") == "user"]
        
        if len(user_messages) > 3:
            result["warnings"].append("消息发送频率较高，建议适当休息")
            result["context_score"] *= 0.9
        
        return result
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0


# 创建全局消息服务实例
message_service = MessageService()
