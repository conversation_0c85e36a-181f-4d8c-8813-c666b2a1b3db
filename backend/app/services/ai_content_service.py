"""
AI内容生成服务
用于动态分享系统的智能内容生成
"""

import json
import re
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

from app.utils.ai_client import OpenRouterClient
from app.models.pet import Pet
from app.schemas.feed import AIContentGenerateRequest, AIContentGenerateResponse


class AIContentService:
    """AI内容生成服务"""
    
    def __init__(self):
        self.ai_client = OpenRouterClient()
        
        # 心情标签映射
        self.mood_keywords = {
            "开心": ["开心", "快乐", "高兴", "愉快", "兴奋", "欢乐", "喜悦"],
            "兴奋": ["兴奋", "激动", "亢奋", "热情", "活跃", "精神"],
            "平静": ["平静", "安静", "宁静", "放松", "悠闲", "淡定"],
            "困倦": ["困", "累", "疲惫", "想睡", "打盹", "懒洋洋"],
            "爱玩": ["玩", "游戏", "运动", "活动", "跑步", "玩耍"],
            "饥饿": ["饿", "想吃", "美食", "零食", "食物", "吃饭"],
            "伤心": ["伤心", "难过", "沮丧", "失落", "郁闷", "不开心"],
            "焦虑": ["焦虑", "紧张", "担心", "不安", "害怕", "恐惧"],
            "慵懒": ["慵懒", "懒散", "悠闲", "舒适", "惬意", "享受"],
            "好奇": ["好奇", "探索", "发现", "新奇", "有趣", "想知道"],
            "满足": ["满足", "满意", "舒服", "惬意", "享受", "幸福"]
        }
        
        # 常见话题标签
        self.topic_keywords = {
            "户外活动": ["公园", "散步", "遛弯", "户外", "草地", "阳光", "新鲜空气"],
            "美食": ["吃", "食物", "零食", "美味", "好吃", "饭", "肉", "鱼"],
            "睡觉": ["睡", "休息", "床", "窝", "打盹", "午睡", "梦"],
            "玩耍": ["玩", "游戏", "球", "玩具", "跑", "跳", "追"],
            "洗澡": ["洗澡", "清洁", "香香", "干净", "洗", "泡澡"],
            "看病": ["医院", "医生", "检查", "打针", "吃药", "看病"],
            "训练": ["训练", "学习", "练习", "技能", "表演", "听话"],
            "社交": ["朋友", "伙伴", "其他", "见面", "社交", "交流"],
            "天气": ["天气", "下雨", "晴天", "阴天", "热", "冷", "风"],
            "家庭": ["主人", "家", "家人", "陪伴", "一起", "温暖"]
        }
    
    async def generate_feed_content(
        self, 
        request: AIContentGenerateRequest, 
        pet: Pet
    ) -> AIContentGenerateResponse:
        """
        生成动态内容
        
        Args:
            request: AI内容生成请求
            pet: 宠物信息
            
        Returns:
            生成的内容响应
        """
        try:
            # 构建提示词
            prompt = self._build_content_prompt(request, pet)
            
            # 调用AI生成内容
            messages = [{"role": "user", "content": prompt}]
            response = await self.ai_client.chat_completion(
                messages=messages,
                temperature=0.8,
                max_tokens=500
            )
            
            if not response:
                raise Exception("AI生成失败")
            
            # 解析AI响应
            content_data = self._parse_ai_response(response)
            
            # 分析心情和标签
            mood = self._analyze_mood(content_data.get("content", ""))
            tags = self._extract_tags(content_data.get("content", ""))
            quality_score = self._calculate_quality_score(content_data.get("content", ""))
            
            return AIContentGenerateResponse(
                content=content_data.get("content", ""),
                mood=mood,
                tags=tags,
                quality_score=quality_score
            )
            
        except Exception as e:
            logger.error(f"AI内容生成失败: {e}")
            # 返回默认内容
            return AIContentGenerateResponse(
                content=f"今天和主人一起度过了美好的时光！我是{pet.name}，一只可爱的{pet.breed}~",
                mood="开心",
                tags=["日常", "陪伴"],
                quality_score=0.7
            )
    
    def _build_content_prompt(self, request: AIContentGenerateRequest, pet: Pet) -> str:
        """构建内容生成提示词"""
        
        # 基础宠物信息
        pet_info = f"宠物名字：{pet.name}\n品种：{pet.breed}\n"
        if pet.personality:
            pet_info += f"性格：{pet.personality}\n"
        if pet.age:
            pet_info += f"年龄：{pet.age_display}\n"
        
        # 心情提示
        mood_hint = ""
        if request.mood:
            mood_hint = f"当前心情：{request.mood}\n"
        
        # 上下文信息
        context_hint = ""
        if request.context:
            context_hint = f"背景信息：{request.context}\n"
        
        # 图片描述提示
        image_hint = ""
        if request.images:
            image_hint = "请根据图片内容生成相应的文案。\n"
        
        prompt = f"""你是一只宠物，请以第一人称的角度写一条动态分享。

{pet_info}{mood_hint}{context_hint}{image_hint}

要求：
1. 以宠物的口吻和视角写作
2. 内容要生动有趣，符合宠物的性格特点
3. 长度控制在50-150字之间
4. 可以使用适当的emoji表情
5. 内容要积极正面，充满生活气息

请直接返回动态内容，不需要其他说明。"""

        return prompt
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """解析AI响应"""
        # 清理响应内容
        content = response.strip()
        
        # 移除可能的引号
        if content.startswith('"') and content.endswith('"'):
            content = content[1:-1]
        
        return {"content": content}
    
    def _analyze_mood(self, content: str) -> str:
        """分析内容中的心情"""
        content_lower = content.lower()
        
        mood_scores = {}
        for mood, keywords in self.mood_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in content_lower:
                    score += 1
            if score > 0:
                mood_scores[mood] = score
        
        if mood_scores:
            # 返回得分最高的心情
            return max(mood_scores, key=mood_scores.get)
        
        return "开心"  # 默认心情
    
    def _extract_tags(self, content: str) -> List[str]:
        """提取内容标签"""
        content_lower = content.lower()
        tags = []
        
        for topic, keywords in self.topic_keywords.items():
            for keyword in keywords:
                if keyword in content_lower:
                    if topic not in tags:
                        tags.append(topic)
                    break
        
        # 如果没有匹配到标签，添加默认标签
        if not tags:
            tags.append("日常")
        
        return tags[:5]  # 最多返回5个标签
    
    def _calculate_quality_score(self, content: str) -> float:
        """计算内容质量评分"""
        score = 0.5  # 基础分
        
        # 长度评分
        length = len(content)
        if 50 <= length <= 150:
            score += 0.2
        elif 30 <= length < 50 or 150 < length <= 200:
            score += 0.1
        
        # emoji使用评分
        emoji_pattern = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]')
        if emoji_pattern.search(content):
            score += 0.1
        
        # 情感词汇评分
        positive_words = ["开心", "快乐", "喜欢", "爱", "美好", "温暖", "舒服", "满足"]
        for word in positive_words:
            if word in content:
                score += 0.05
                break
        
        # 互动性评分
        interactive_words = ["主人", "一起", "陪伴", "分享", "告诉"]
        for word in interactive_words:
            if word in content:
                score += 0.05
                break
        
        return min(score, 1.0)  # 最高1.0分
    
    async def generate_image_description(self, image_urls: List[str], pet: Pet) -> str:
        """
        基于图片生成描述
        注意：这里是模拟实现，实际需要图片识别API
        """
        # 模拟图片分析结果
        descriptions = [
            f"我在这张照片里看起来超级可爱！",
            f"这是我最喜欢的地方，总是让我感到很舒服。",
            f"主人给我拍了这张美美的照片~",
            f"看我这个姿势，是不是很帅气？",
            f"这个角度拍出来的我特别上镜！"
        ]
        
        import random
        return random.choice(descriptions)
    
    async def suggest_tags_for_content(self, content: str) -> List[str]:
        """为内容推荐标签"""
        return self._extract_tags(content)
