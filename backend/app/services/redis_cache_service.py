"""
Redis缓存服务
提供高性能的数据缓存功能
"""

import json
import pickle
import time
from typing import Any, Optional, Dict, List, Union
from functools import wraps
import hashlib
import asyncio
from loguru import logger

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis未安装，缓存功能将被禁用")

from app.core.config import settings


class RedisCacheService:
    """Redis缓存服务"""
    
    def __init__(self):
        self.redis_client = None
        self.enabled = REDIS_AVAILABLE and hasattr(settings, 'REDIS_URL')
        self.default_ttl = 300  # 5分钟默认过期时间
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        
        if self.enabled:
            self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            redis_url = getattr(settings, 'REDIS_URL', 'redis://localhost:6379/0')
            self.redis_client = redis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            logger.info("Redis缓存服务初始化成功")
        except Exception as e:
            logger.error(f"Redis初始化失败: {e}")
            self.enabled = False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if not self.enabled:
            return None
        
        try:
            data = await self.redis_client.get(key)
            if data is not None:
                self.cache_stats["hits"] += 1
                return json.loads(data)
            else:
                self.cache_stats["misses"] += 1
                return None
        except Exception as e:
            logger.error(f"Redis获取数据失败: {e}")
            self.cache_stats["misses"] += 1
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存数据"""
        if not self.enabled:
            return False
        
        try:
            ttl = ttl or self.default_ttl
            data = json.dumps(value, default=str)
            await self.redis_client.setex(key, ttl, data)
            self.cache_stats["sets"] += 1
            return True
        except Exception as e:
            logger.error(f"Redis设置数据失败: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存数据"""
        if not self.enabled:
            return False
        
        try:
            result = await self.redis_client.delete(key)
            if result:
                self.cache_stats["deletes"] += 1
            return bool(result)
        except Exception as e:
            logger.error(f"Redis删除数据失败: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self.enabled:
            return False
        
        try:
            return bool(await self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"Redis检查键存在失败: {e}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置键的过期时间"""
        if not self.enabled:
            return False
        
        try:
            return bool(await self.redis_client.expire(key, ttl))
        except Exception as e:
            logger.error(f"Redis设置过期时间失败: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """递增计数器"""
        if not self.enabled:
            return None
        
        try:
            return await self.redis_client.incrby(key, amount)
        except Exception as e:
            logger.error(f"Redis递增失败: {e}")
            return None
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存数据"""
        if not self.enabled or not keys:
            return {}
        
        try:
            values = await self.redis_client.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                if value is not None:
                    try:
                        result[key] = json.loads(value)
                        self.cache_stats["hits"] += 1
                    except json.JSONDecodeError:
                        self.cache_stats["misses"] += 1
                else:
                    self.cache_stats["misses"] += 1
            return result
        except Exception as e:
            logger.error(f"Redis批量获取失败: {e}")
            self.cache_stats["misses"] += len(keys)
            return {}
    
    async def set_many(
        self, 
        data: Dict[str, Any], 
        ttl: Optional[int] = None
    ) -> bool:
        """批量设置缓存数据"""
        if not self.enabled or not data:
            return False
        
        try:
            ttl = ttl or self.default_ttl
            pipe = self.redis_client.pipeline()
            
            for key, value in data.items():
                json_data = json.dumps(value, default=str)
                pipe.setex(key, ttl, json_data)
            
            await pipe.execute()
            self.cache_stats["sets"] += len(data)
            return True
        except Exception as e:
            logger.error(f"Redis批量设置失败: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的所有键"""
        if not self.enabled:
            return 0
        
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                deleted = await self.redis_client.delete(*keys)
                self.cache_stats["deletes"] += deleted
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Redis清除模式失败: {e}")
            return 0
    
    def cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        # 创建唯一的缓存键
        key_parts = [prefix]
        
        # 添加位置参数
        for arg in args:
            if isinstance(arg, (int, str, float)):
                key_parts.append(str(arg))
            else:
                key_parts.append(hashlib.md5(str(arg).encode()).hexdigest()[:8])
        
        # 添加关键字参数
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            kwargs_str = "&".join(f"{k}={v}" for k, v in sorted_kwargs)
            key_parts.append(hashlib.md5(kwargs_str.encode()).hexdigest()[:8])
        
        return ":".join(key_parts)
    
    def cache_decorator(
        self, 
        prefix: str, 
        ttl: Optional[int] = None,
        key_func: Optional[callable] = None
    ):
        """缓存装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = self.cache_key(prefix, *args, **kwargs)
                
                # 尝试从缓存获取
                cached_result = await self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数
                result = await func(*args, **kwargs)
                
                # 缓存结果
                if result is not None:
                    await self.set(cache_key, result, ttl)
                
                return result
            return wrapper
        return decorator
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self.cache_stats.copy()
        
        # 计算命中率
        total_requests = stats["hits"] + stats["misses"]
        if total_requests > 0:
            stats["hit_rate"] = stats["hits"] / total_requests
        else:
            stats["hit_rate"] = 0
        
        # Redis服务器信息
        if self.enabled and self.redis_client:
            try:
                info = await self.redis_client.info()
                stats["redis_info"] = {
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory": info.get("used_memory_human", "0B"),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                    "uptime_in_seconds": info.get("uptime_in_seconds", 0)
                }
            except Exception as e:
                logger.error(f"获取Redis信息失败: {e}")
                stats["redis_info"] = {"error": str(e)}
        
        return stats
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.enabled:
            return False
        
        try:
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis健康检查失败: {e}")
            return False


class CacheStrategy:
    """缓存策略管理"""
    
    # 缓存策略配置
    STRATEGIES = {
        "feeds_list": {"ttl": 60, "prefix": "feeds:list"},
        "feeds_trending": {"ttl": 300, "prefix": "feeds:trending"},
        "feeds_detail": {"ttl": 180, "prefix": "feeds:detail"},
        "user_profile": {"ttl": 600, "prefix": "user:profile"},
        "pet_info": {"ttl": 1800, "prefix": "pet:info"},
        "ai_conversation": {"ttl": 3600, "prefix": "ai:conversation"},
        "stats": {"ttl": 120, "prefix": "stats"}
    }
    
    @classmethod
    def get_strategy(cls, strategy_name: str) -> Dict[str, Any]:
        """获取缓存策略"""
        return cls.STRATEGIES.get(strategy_name, {"ttl": 300, "prefix": "default"})
    
    @classmethod
    def feeds_list_key(cls, page: int, size: int, **filters) -> str:
        """动态列表缓存键"""
        strategy = cls.get_strategy("feeds_list")
        key_parts = [strategy["prefix"], f"page:{page}", f"size:{size}"]
        
        if filters:
            filter_str = "&".join(f"{k}:{v}" for k, v in sorted(filters.items()))
            key_parts.append(hashlib.md5(filter_str.encode()).hexdigest()[:8])
        
        return ":".join(key_parts)
    
    @classmethod
    def trending_feeds_key(cls, days: int = 7) -> str:
        """热门动态缓存键"""
        strategy = cls.get_strategy("feeds_trending")
        return f"{strategy['prefix']}:days:{days}"
    
    @classmethod
    def user_profile_key(cls, user_id: int) -> str:
        """用户资料缓存键"""
        strategy = cls.get_strategy("user_profile")
        return f"{strategy['prefix']}:{user_id}"


# 全局缓存服务实例
cache_service = RedisCacheService()
cache_strategy = CacheStrategy()
