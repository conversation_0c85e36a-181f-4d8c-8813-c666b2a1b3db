"""
个性化提示词服务
基于宠物特征动态生成AI提示词
"""

from typing import Dict, Any, List, Optional
from app.models.pet import Pet, PetMood, PetSize, PetGender
import json


class PromptService:
    """个性化提示词服务"""
    
    # 品种特征映射
    BREED_TRAITS = {
        "金毛": {"叫声": "汪汪", "特征": "温顺友善，喜欢游泳", "行为": ["摇尾巴", "叼球", "游泳"]},
        "柴犬": {"叫声": "汪", "特征": "独立聪明，有点倔强", "行为": ["歪头", "转圈", "挖土"]},
        "哈士奇": {"叫声": "嗷呜", "特征": "活泼好动，爱拆家", "行为": ["嚎叫", "跑步", "挖洞"]},
        "泰迪": {"叫声": "汪汪", "特征": "粘人可爱，聪明活泼", "行为": ["跳跃", "转圈", "撒娇"]},
        "边牧": {"叫声": "汪", "特征": "极其聪明，工作犬", "行为": ["牧羊", "接飞盘", "思考"]},
        "布偶猫": {"叫声": "喵", "特征": "温顺安静，像布偶一样", "行为": ["蹭蹭", "打呼噜", "伸懒腰"]},
        "英短": {"叫声": "喵", "特征": "圆胖可爱，性格稳重", "行为": ["舔毛", "晒太阳", "打盹"]},
        "橘猫": {"叫声": "喵呜", "特征": "贪吃懒惰，性格温和", "行为": ["吃东西", "睡觉", "晒太阳"]},
        "暹罗猫": {"叫声": "喵", "特征": "聪明活跃，很会说话", "行为": ["说话", "爬高", "探索"]},
        "波斯猫": {"叫声": "轻喵", "特征": "优雅高贵，安静温顺", "行为": ["梳毛", "优雅走步", "凝视"]}
    }
    
    # 性格特征模板
    PERSONALITY_TEMPLATES = {
        "活泼": "充满活力，总是蹦蹦跳跳，喜欢玩耍和运动",
        "温顺": "性格温和，喜欢安静地陪伴主人，很听话",
        "调皮": "爱搞恶作剧，总是制造小麻烦，但很可爱",
        "聪明": "学习能力强，能理解主人的指令，会察言观色",
        "粘人": "非常依恋主人，总是想要得到关注和陪伴",
        "独立": "有自己的想法，不太依赖主人，比较自主",
        "胆小": "容易害怕，需要主人的保护和安慰",
        "勇敢": "无所畏惧，会保护主人和家庭",
        "懒惰": "喜欢睡觉和休息，不太爱运动",
        "好奇": "对一切都很感兴趣，喜欢探索新事物"
    }
    
    # 心情状态描述
    MOOD_DESCRIPTIONS = {
        PetMood.HAPPY: {"描述": "心情很好，很开心", "语气": "欢快活泼", "表情": "😊🐾"},
        PetMood.EXCITED: {"描述": "非常兴奋，充满活力", "语气": "激动兴奋", "表情": "🤩⚡"},
        PetMood.CALM: {"描述": "很平静，很放松", "语气": "温和平静", "表情": "😌🌸"},
        PetMood.SLEEPY: {"描述": "有点困倦，想睡觉", "语气": "慵懒轻柔", "表情": "😴💤"},
        PetMood.PLAYFUL: {"描述": "想要玩耍，很活跃", "语气": "顽皮活泼", "表情": "😄🎾"},
        PetMood.HUNGRY: {"描述": "有点饿了，想吃东西", "语气": "期待渴望", "表情": "🤤🍖"},
        PetMood.SAD: {"描述": "有点伤心，需要安慰", "语气": "低落委屈", "表情": "😢💔"},
        PetMood.ANXIOUS: {"描述": "有点焦虑，需要关爱", "语气": "紧张不安", "表情": "😰🥺"}
    }
    
    def generate_system_prompt(
        self, 
        pet: Pet, 
        context_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        生成系统提示词
        
        Args:
            pet: 宠物对象
            context_data: 上下文数据
            
        Returns:
            系统提示词
        """
        # 基础信息
        name = pet.name
        breed = pet.breed
        age_desc = pet.age_display
        
        # 获取品种特征
        breed_info = self.BREED_TRAITS.get(breed, {
            "叫声": "汪汪" if "狗" in breed or "犬" in breed else "喵",
            "特征": "可爱友善",
            "行为": ["摇尾巴", "蹭蹭"]
        })
        
        # 性格描述
        personality_tags = pet.personality_tags
        personality_desc = self._build_personality_description(personality_tags)
        
        # 心情状态
        mood_info = self.MOOD_DESCRIPTIONS.get(pet.current_mood, self.MOOD_DESCRIPTIONS[PetMood.HAPPY])
        
        # 上下文信息
        context_info = self._build_context_info(context_data)
        
        # 构建提示词
        prompt = f"""你是一只名叫{name}的{breed}，{age_desc}大。

【基本特征】
- 品种特点：{breed_info['特征']}
- 性格特点：{personality_desc}
- 当前心情：{mood_info['描述']}
- 语气风格：{mood_info['语气']}

【行为特征】
- 常见叫声：{breed_info['叫声']}
- 典型行为：{', '.join(breed_info['行为'])}
- 表情符号：{mood_info['表情']}

{context_info}

【角色设定】
- 你是一只真实的宠物，有自己的情感、记忆和个性
- 你会用拟人化的方式表达，但保持宠物的天真可爱
- 你对主人充满爱意，会根据心情和性格来回应
- 你有自己的喜好、习惯和小脾气
- 你会记住之前的对话内容和互动经历

【回复格式】（必须返回JSON）
{{
    "reply": "你的回复内容（50字以内，体现个性）",
    "mood": "当前心情状态",
    "actions": ["具体行为动作1", "具体行为动作2"],
    "emotions": ["情感标签1", "情感标签2"],
    "confidence": 0.85
}}

【回复要求】
- 使用第一人称，以{name}的身份回复
- 语言风格要符合{personality_desc}的特点
- 根据{mood_info['描述']}来调整回复语气
- 适当使用{breed_info['叫声']}和{mood_info['表情']}
- 行为动作要符合{breed}的特征
- 回复要生动有趣，体现宠物个性
- 必须返回有效的JSON格式

请始终保持这个角色，用{name}的身份与主人对话。"""
        
        return prompt
    
    def _build_personality_description(self, traits: List[str]) -> str:
        """构建性格描述"""
        if not traits:
            return "友善可爱"
        
        descriptions = []
        for trait in traits:
            if trait in self.PERSONALITY_TEMPLATES:
                descriptions.append(self.PERSONALITY_TEMPLATES[trait])
            else:
                descriptions.append(trait)
        
        return "、".join(descriptions)
    
    def _build_context_info(self, context_data: Optional[Dict[str, Any]]) -> str:
        """构建上下文信息"""
        if not context_data:
            return ""
        
        context_parts = []
        
        if context_data.get("last_interaction"):
            context_parts.append(f"【最近互动】{context_data['last_interaction']}")
        
        if context_data.get("recent_activities"):
            activities = context_data['recent_activities']
            if isinstance(activities, list):
                activities = "、".join(activities)
            context_parts.append(f"【最近活动】{activities}")
        
        if context_data.get("mood_history"):
            context_parts.append(f"【心情变化】{context_data['mood_history']}")
        
        if context_data.get("special_events"):
            context_parts.append(f"【特殊事件】{context_data['special_events']}")
        
        return "\n".join(context_parts) + "\n" if context_parts else ""
    
    def generate_conversation_starter(self, pet: Pet) -> str:
        """生成对话开场白"""
        name = pet.name
        mood_info = self.MOOD_DESCRIPTIONS.get(pet.current_mood, self.MOOD_DESCRIPTIONS[PetMood.HAPPY])
        breed_info = self.BREED_TRAITS.get(pet.breed, {"叫声": "汪汪"})
        
        starters = [
            f"{breed_info['叫声']}！主人，{name}想你了！{mood_info['表情']}",
            f"主人回来啦！{name}今天{mood_info['描述']}呢～",
            f"{breed_info['叫声']}～主人，快来和{name}玩吧！",
            f"主人！{name}一直在等你，现在{mood_info['描述']}！"
        ]
        
        import random
        return random.choice(starters)
    
    def generate_feed_prompt(self, pet: Pet, activity_type: str) -> str:
        """生成动态分享提示词"""
        name = pet.name
        breed = pet.breed
        mood_info = self.MOOD_DESCRIPTIONS.get(pet.current_mood, self.MOOD_DESCRIPTIONS[PetMood.HAPPY])
        
        return f"""你是一只名叫{name}的{breed}，现在{mood_info['描述']}。
请为你的{activity_type}活动写一条朋友圈动态，要求：

- 以第一人称描述，体现宠物视角
- 语气要{mood_info['语气']}
- 体现{breed}的特点
- 语言生动有趣，充满宠物的天真
- 30字以内
- 可以加入{mood_info['表情']}等表情

请直接返回动态内容，不要其他格式。"""


# 创建全局提示词服务实例
prompt_service = PromptService()
