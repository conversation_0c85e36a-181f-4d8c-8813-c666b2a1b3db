"""
查询优化工具
提供常用的查询优化模式和缓存策略
"""

from typing import List, Dict, Any, Optional, Callable
from functools import wraps
import time
import hashlib
import json
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import select, func, and_, or_
from loguru import logger

from app.models.feed import Feed, FeedLike, FeedComment
from app.models.pet import Pet
from app.models.user import User
from app.models.message import Message
from app.models.conversation import Conversation


class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self):
        self.query_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
    
    def cache_query(self, ttl: int = 300):
        """查询结果缓存装饰器"""
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = self._generate_cache_key(func.__name__, args, kwargs)
                
                # 检查缓存
                if cache_key in self.query_cache:
                    cached_result, timestamp = self.query_cache[cache_key]
                    if time.time() - timestamp < ttl:
                        logger.debug(f"查询缓存命中: {func.__name__}")
                        return cached_result
                
                # 执行查询
                start_time = time.time()
                result = await func(*args, **kwargs)
                query_time = time.time() - start_time
                
                # 缓存结果
                self.query_cache[cache_key] = (result, time.time())
                
                # 记录慢查询
                if query_time > 1.0:
                    logger.warning(f"慢查询检测: {func.__name__} 耗时 {query_time:.2f}s")
                
                return result
            return wrapper
        return decorator
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        # 过滤掉数据库会话对象
        filtered_args = [arg for arg in args if not isinstance(arg, Session)]
        cache_data = {
            "func": func_name,
            "args": str(filtered_args),
            "kwargs": {k: v for k, v in kwargs.items() if k != "db"}
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def optimize_feed_queries(self, db: Session):
        """优化动态查询"""
        return FeedQueryOptimizer(db)
    
    def optimize_user_queries(self, db: Session):
        """优化用户查询"""
        return UserQueryOptimizer(db)
    
    def optimize_pet_queries(self, db: Session):
        """优化宠物查询"""
        return PetQueryOptimizer(db)
    
    def optimize_ai_queries(self, db: Session):
        """优化AI对话查询"""
        return AIQueryOptimizer(db)


class FeedQueryOptimizer:
    """动态查询优化器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_feeds_with_stats(
        self, 
        user_id: Optional[int] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Feed]:
        """获取带统计信息的动态列表（优化版）"""
        query = self.db.query(Feed).options(
            joinedload(Feed.pet),
            joinedload(Feed.user),
            selectinload(Feed.likes),
            selectinload(Feed.comments)
        )
        
        # 基础过滤
        query = query.filter(Feed.status == "published", Feed.is_public == True)
        
        # 用户过滤
        if user_id:
            query = query.filter(
                or_(Feed.is_public == True, Feed.user_id == user_id)
            )
        
        # 排序和分页
        query = query.order_by(Feed.created_at.desc())
        query = query.offset(offset).limit(limit)
        
        return query.all()
    
    def get_trending_feeds_optimized(
        self, 
        days: int = 7,
        limit: int = 20
    ) -> List[Feed]:
        """获取热门动态（优化版）"""
        from datetime import datetime, timedelta
        
        since_date = datetime.utcnow() - timedelta(days=days)
        
        # 使用子查询优化
        subquery = self.db.query(
            Feed.id,
            (Feed.likes_count * 2 + 
             Feed.comments_count * 3 + 
             Feed.shares_count * 5 + 
             Feed.views_count * 0.1).label('trending_score')
        ).filter(
            Feed.status == "published",
            Feed.is_public == True,
            Feed.created_at >= since_date
        ).subquery()
        
        # 主查询
        query = self.db.query(Feed).options(
            joinedload(Feed.pet),
            joinedload(Feed.user)
        ).join(subquery, Feed.id == subquery.c.id).order_by(
            subquery.c.trending_score.desc()
        ).limit(limit)
        
        return query.all()
    
    def get_user_feed_stats(self, user_id: int) -> Dict[str, Any]:
        """获取用户动态统计（优化版）"""
        stats = self.db.query(
            func.count(Feed.id).label('total_feeds'),
            func.sum(Feed.likes_count).label('total_likes'),
            func.sum(Feed.comments_count).label('total_comments'),
            func.sum(Feed.views_count).label('total_views')
        ).filter(Feed.user_id == user_id).first()
        
        return {
            "total_feeds": stats.total_feeds or 0,
            "total_likes": stats.total_likes or 0,
            "total_comments": stats.total_comments or 0,
            "total_views": stats.total_views or 0
        }


class UserQueryOptimizer:
    """用户查询优化器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_with_pets(self, user_id: int) -> Optional[User]:
        """获取用户及其宠物信息（优化版）"""
        return self.db.query(User).options(
            selectinload(User.pets).selectinload(Pet.photos)
        ).filter(User.id == user_id).first()
    
    def get_active_users_with_stats(self, limit: int = 50) -> List[User]:
        """获取活跃用户及统计信息（优化版）"""
        return self.db.query(User).options(
            selectinload(User.pets),
            selectinload(User.feeds)
        ).filter(
            User.is_active == True
        ).order_by(User.created_at.desc()).limit(limit).all()


class PetQueryOptimizer:
    """宠物查询优化器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_pets_with_recent_activity(
        self, 
        user_id: int,
        days: int = 30
    ) -> List[Pet]:
        """获取有近期活动的宠物（优化版）"""
        from datetime import datetime, timedelta
        
        since_date = datetime.utcnow() - timedelta(days=days)
        
        return self.db.query(Pet).options(
            selectinload(Pet.photos),
            selectinload(Pet.feeds.and_(Feed.created_at >= since_date))
        ).filter(
            Pet.owner_id == user_id,
            Pet.is_active == True
        ).order_by(Pet.updated_at.desc()).all()
    
    def get_pet_interaction_stats(self, pet_id: int) -> Dict[str, Any]:
        """获取宠物互动统计（优化版）"""
        # 动态统计
        feed_stats = self.db.query(
            func.count(Feed.id).label('feed_count'),
            func.sum(Feed.likes_count).label('total_likes'),
            func.sum(Feed.comments_count).label('total_comments')
        ).filter(Feed.pet_id == pet_id).first()
        
        # 对话统计
        conversation_stats = self.db.query(
            func.count(Conversation.id).label('conversation_count'),
            func.sum(Conversation.message_count).label('total_messages')
        ).filter(Conversation.pet_id == pet_id).first()
        
        return {
            "feeds": {
                "count": feed_stats.feed_count or 0,
                "total_likes": feed_stats.total_likes or 0,
                "total_comments": feed_stats.total_comments or 0
            },
            "conversations": {
                "count": conversation_stats.conversation_count or 0,
                "total_messages": conversation_stats.total_messages or 0
            }
        }


class AIQueryOptimizer:
    """AI对话查询优化器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_conversation_with_recent_messages(
        self, 
        conversation_id: int,
        message_limit: int = 50
    ) -> Optional[Conversation]:
        """获取对话及最近消息（优化版）"""
        return self.db.query(Conversation).options(
            selectinload(Conversation.messages.limit(message_limit).order_by(
                Message.created_at.desc()
            )),
            joinedload(Conversation.user),
            joinedload(Conversation.pet)
        ).filter(Conversation.id == conversation_id).first()
    
    def get_user_conversation_summary(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户对话摘要（优化版）"""
        conversations = self.db.query(Conversation).options(
            joinedload(Conversation.pet),
            selectinload(Conversation.messages.limit(1).order_by(
                Message.created_at.desc()
            ))
        ).filter(
            Conversation.user_id == user_id,
            Conversation.is_active == True
        ).order_by(Conversation.updated_at.desc()).all()
        
        summaries = []
        for conv in conversations:
            last_message = conv.messages[0] if conv.messages else None
            summaries.append({
                "conversation_id": conv.id,
                "pet_name": conv.pet.name if conv.pet else "Unknown",
                "message_count": conv.message_count,
                "last_message": last_message.content if last_message else None,
                "last_activity": conv.updated_at
            })
        
        return summaries


# 全局查询优化器实例
query_optimizer = QueryOptimizer()
