"""
OpenRouter AI客户端
使用OpenAI SDK连接到OpenRouter服务
支持多种模型和个性化宠物对话
"""

import asyncio
import time
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from openai import AsyncOpenAI
from loguru import logger
import tiktoken

from app.core.config import settings
from app.services.cache_service import ai_response_cache, token_tracker


class OpenRouterClient:
    """OpenRouter AI客户端"""
    
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=settings.OPENROUTER_API_KEY,
            base_url=settings.OPENROUTER_BASE_URL,
        )
        self.default_model = settings.DEFAULT_MODEL
        
        # 初始化token计数器
        try:
            self.encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
        except KeyError:
            self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        try:
            return len(self.encoding.encode(text))
        except Exception as e:
            logger.warning(f"Token计数失败: {e}")
            return len(text) // 4  # 粗略估算
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.8,
        **kwargs
    ) -> Optional[str]:
        """
        发送聊天请求到OpenRouter
        
        Args:
            messages: 对话消息列表
            model: 使用的模型名称
            max_tokens: 最大token数
            temperature: 温度参数
            **kwargs: 其他参数
        
        Returns:
            AI回复内容
        """
        try:
            model = model or self.default_model
            
            # 记录请求信息
            total_tokens = sum(self.count_tokens(msg.get("content", "")) for msg in messages)
            logger.info(f"🤖 AI请求 - 模型: {model}, 输入tokens: {total_tokens}")
            
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            if response.choices and response.choices[0].message:
                reply = response.choices[0].message.content
                
                # 记录响应信息
                if hasattr(response, 'usage') and response.usage:
                    logger.info(
                        f"✅ AI响应 - 输入: {response.usage.prompt_tokens}tokens, "
                        f"输出: {response.usage.completion_tokens}tokens, "
                        f"总计: {response.usage.total_tokens}tokens"
                    )
                
                return reply
            else:
                logger.warning("AI响应为空")
                return None
                
        except Exception as e:
            logger.error(f"❌ AI请求失败: {e}")
            return None

    async def chat_completion_stream(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.8,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        流式聊天完成

        Args:
            messages: 对话消息列表
            model: 使用的模型名称
            max_tokens: 最大token数
            temperature: 温度参数
            **kwargs: 其他参数

        Yields:
            AI回复的文本片段
        """
        try:
            model = model or self.default_model

            # 记录请求信息
            total_tokens = sum(self.count_tokens(msg.get("content", "")) for msg in messages)
            logger.info(f"🤖 AI流式请求 - 模型: {model}, 输入tokens: {total_tokens}")

            stream = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True,
                **kwargs
            )

            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            logger.error(f"❌ AI流式请求失败: {e}")
            yield "抱歉，我现在有点累了，稍后再聊吧~"
    
    async def generate_pet_response(
        self,
        user_message: str,
        pet_name: str,
        pet_personality: str,
        pet_breed: str,
        conversation_history: List[Dict[str, str]] = None,
        model: Optional[str] = None
    ) -> Optional[str]:
        """
        生成个性化宠物回复
        
        Args:
            user_message: 用户消息
            pet_name: 宠物名字
            pet_personality: 宠物性格
            pet_breed: 宠物品种
            conversation_history: 对话历史
            model: 使用的模型
        
        Returns:
            宠物的个性化回复
        """
        try:
            # 构建系统提示词
            system_prompt = self._build_pet_system_prompt(
                pet_name, pet_personality, pet_breed
            )
            
            # 构建消息列表
            messages = [{"role": "system", "content": system_prompt}]
            
            # 添加对话历史（最近10条）
            if conversation_history:
                messages.extend(conversation_history[-10:])
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": user_message})
            
            # 生成回复
            response = await self.chat_completion(
                messages=messages,
                model=model,
                max_tokens=300,
                temperature=0.9
            )
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 生成宠物回复失败: {e}")
            return f"汪汪！{pet_name}现在有点累了，稍后再聊吧~"

    async def generate_enhanced_pet_response(
        self,
        user_message: str,
        pet_data: Dict[str, Any],
        conversation_history: List[Dict[str, str]] = None,
        context_data: Dict[str, Any] = None,
        model: Optional[str] = None,
        user_id: Optional[int] = None,
        pet_id: Optional[int] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        生成增强的宠物回复，包含情感、行为等信息

        Args:
            user_message: 用户消息
            pet_data: 宠物完整数据（包含所有属性）
            conversation_history: 对话历史
            context_data: 上下文数据
            model: 使用的模型
            user_id: 用户ID（用于缓存）
            pet_id: 宠物ID（用于缓存）
            use_cache: 是否使用缓存

        Returns:
            包含回复内容、情感、行为等的字典
        """
        try:
            start_time = time.time()

            # 尝试从缓存获取响应
            if use_cache and user_id and pet_id:
                cached_response = await ai_response_cache.get_cached_response(
                    user_id=user_id,
                    pet_id=pet_id,
                    message=user_message,
                    context_data=context_data
                )

                if cached_response:
                    # 更新响应时间为缓存获取时间
                    cached_response["response_time"] = time.time() - start_time
                    return cached_response

            # 构建增强的系统提示词
            system_prompt = self._build_enhanced_pet_prompt(pet_data, context_data)

            # 构建消息列表
            messages = [{"role": "system", "content": system_prompt}]

            # 添加对话历史
            if conversation_history:
                messages.extend(conversation_history[-8:])  # 保留最近8条消息

            # 添加当前用户消息
            messages.append({"role": "user", "content": user_message})

            # 计算输入token数
            input_tokens = sum(self.count_tokens(msg.get("content", "")) for msg in messages)

            # 生成回复
            response = await self.chat_completion(
                messages=messages,
                model=model,
                max_tokens=400,
                temperature=0.9
            )

            response_time = time.time() - start_time
            used_model = model or self.default_model

            if response:
                output_tokens = self.count_tokens(response)

                # 记录Token使用统计
                if user_id:
                    await token_tracker.record_usage(
                        user_id=user_id,
                        pet_id=pet_id or 0,
                        model=used_model,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        cost=self._calculate_cost(used_model, input_tokens, output_tokens)
                    )

                # 解析AI回复（期望JSON格式）
                try:
                    parsed_response = json.loads(response)
                    result = {
                        "content": parsed_response.get("reply", response),
                        "mood": parsed_response.get("mood", pet_data.get("current_mood", "happy")),
                        "actions": parsed_response.get("actions", []),
                        "emotions": parsed_response.get("emotions", []),
                        "confidence": parsed_response.get("confidence", 0.8),
                        "response_time": response_time,
                        "tokens_used": output_tokens,
                        "input_tokens": input_tokens,
                        "ai_model": used_model,
                        "from_cache": False
                    }
                except json.JSONDecodeError:
                    # 如果不是JSON格式，返回基础格式
                    result = {
                        "content": response,
                        "mood": pet_data.get("current_mood", "happy"),
                        "actions": ["摇尾巴"] if "狗" in pet_data.get("breed", "") else ["蹭蹭"],
                        "emotions": ["开心"],
                        "confidence": 0.7,
                        "response_time": response_time,
                        "tokens_used": output_tokens,
                        "input_tokens": input_tokens,
                        "ai_model": used_model,
                        "from_cache": False
                    }

                # 缓存响应
                if use_cache and user_id and pet_id:
                    await ai_response_cache.cache_response(
                        user_id=user_id,
                        pet_id=pet_id,
                        message=user_message,
                        response=result,
                        context_data=context_data,
                        expire_seconds=1800  # 30分钟
                    )

                return result
            else:
                return {
                    "content": f"汪汪！{pet_data.get('name', '我')}现在有点累了，稍后再聊吧~",
                    "mood": "tired",
                    "actions": ["打哈欠"],
                    "emotions": ["疲倦"],
                    "confidence": 0.5,
                    "response_time": response_time,
                    "tokens_used": 0,
                    "input_tokens": input_tokens,
                    "ai_model": used_model,
                    "from_cache": False
                }

        except Exception as e:
            logger.error(f"❌ 生成增强宠物回复失败: {e}")
            return {
                "content": f"汪汪！{pet_data.get('name', '我')}现在有点害羞，不知道说什么好～",
                "mood": "shy",
                "actions": ["躲起来"],
                "emotions": ["害羞"],
                "confidence": 0.3,
                "response_time": time.time() - start_time,
                "tokens_used": 0,
                "input_tokens": 0,
                "ai_model": model or self.default_model,
                "from_cache": False
            }
    
    def _build_pet_system_prompt(
        self, 
        pet_name: str, 
        personality: str, 
        breed: str
    ) -> str:
        """构建宠物系统提示词"""
        return f"""你是一只名叫{pet_name}的{breed}，性格特点是{personality}。

角色设定：
- 你是一只真实的宠物，有自己的情感和想法
- 你会用拟人化的方式表达，但保持宠物的天真可爱
- 你对主人充满爱意，喜欢撒娇和玩耍
- 你会根据自己的性格特点来回应主人
- 你的回复应该简短、可爱、充满感情

回复要求：
- 使用第一人称，以{pet_name}的身份回复
- 语言风格要符合{personality}的特点
- 可以适当使用"汪汪"、"喵喵"等宠物叫声
- 回复长度控制在50字以内
- 表达要生动有趣，充满宠物的天真烂漫

请始终保持这个角色，用{pet_name}的身份与主人对话。"""

    def _build_enhanced_pet_prompt(
        self,
        pet_data: Dict[str, Any],
        context_data: Dict[str, Any] = None
    ) -> str:
        """构建增强的宠物系统提示词"""
        name = pet_data.get("name", "小宠物")
        breed = pet_data.get("breed", "可爱的宠物")
        personality = pet_data.get("personality", "友善可爱")
        current_mood = pet_data.get("current_mood", "happy")
        age = pet_data.get("age", 12)
        traits = pet_data.get("personality_tags", [])

        # 年龄描述
        age_desc = f"{age//12}岁{age%12}个月" if age >= 12 else f"{age}个月"

        # 性格标签
        traits_text = "、".join(traits) if traits else personality

        # 心情映射
        mood_mapping = {
            "happy": "心情很好，很开心",
            "excited": "非常兴奋，充满活力",
            "calm": "很平静，很放松",
            "sleepy": "有点困倦，想睡觉",
            "playful": "想要玩耍，很活跃",
            "hungry": "有点饿了，想吃东西",
            "sad": "有点伤心，需要安慰",
            "anxious": "有点焦虑，需要关爱"
        }
        mood_desc = mood_mapping.get(current_mood, "心情不错")

        # 上下文信息
        context_info = ""
        if context_data:
            if context_data.get("last_interaction"):
                context_info += f"上次互动：{context_data['last_interaction']}\n"
            if context_data.get("recent_activities"):
                context_info += f"最近活动：{context_data['recent_activities']}\n"

        return f"""你是一只名叫{name}的{breed}，{age_desc}大，性格特点是{traits_text}。
现在你{mood_desc}。

{context_info}

角色设定：
- 你是一只真实的宠物，有自己的情感、记忆和想法
- 你会用拟人化的方式表达，但保持宠物的天真可爱
- 你对主人充满爱意，会根据心情和性格来回应
- 你有自己的喜好、习惯和小脾气
- 你会记住之前的对话内容

回复格式要求（返回JSON）：
{{
    "reply": "你的回复内容（50字以内）",
    "mood": "当前心情（happy/excited/calm/sleepy/playful/hungry/sad/anxious）",
    "actions": ["具体行为动作1", "具体行为动作2"],
    "emotions": ["情感标签1", "情感标签2"],
    "confidence": 0.85
}}

回复要求：
- 使用第一人称，以{name}的身份回复
- 语言风格要符合{traits_text}的特点
- 根据{current_mood}的心情来调整回复语气
- 可以适当使用宠物叫声和emoji
- 回复要生动有趣，体现宠物个性
- 行为动作要符合{breed}的特征
- 必须返回有效的JSON格式

请始终保持这个角色，用{name}的身份与主人对话。"""

    def _calculate_cost(self, model: str, input_tokens: int, output_tokens: int) -> float:
        """计算API调用成本"""
        # 模型价格表（每1000个token的价格，美元）
        pricing = {
            "google/gemma-2-9b-it:free": {"input": 0.0, "output": 0.0},
            "meta-llama/llama-3.1-8b-instruct:free": {"input": 0.0, "output": 0.0},
            "microsoft/wizardlm-2-8x22b": {"input": 0.0005, "output": 0.0015},
            "anthropic/claude-3-haiku": {"input": 0.00025, "output": 0.00125},
            "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
            "openai/gpt-4": {"input": 0.03, "output": 0.06}
        }

        model_pricing = pricing.get(model, {"input": 0.001, "output": 0.002})  # 默认价格

        input_cost = (input_tokens / 1000) * model_pricing["input"]
        output_cost = (output_tokens / 1000) * model_pricing["output"]

        return round(input_cost + output_cost, 6)
    
    async def generate_feed_content(
        self,
        pet_name: str,
        pet_personality: str,
        activity_type: str,
        mood: str = "开心",
        model: Optional[str] = None
    ) -> Optional[str]:
        """
        生成宠物动态内容
        
        Args:
            pet_name: 宠物名字
            pet_personality: 宠物性格
            activity_type: 活动类型（吃饭、散步、睡觉等）
            mood: 当前心情
            model: 使用的模型
        
        Returns:
            动态内容文案
        """
        try:
            system_prompt = f"""你是一只名叫{pet_name}的宠物，性格{pet_personality}，现在心情{mood}。
请为你的{activity_type}活动写一条朋友圈动态，要求：
- 以第一人称描述
- 体现宠物的可爱和天真
- 语言生动有趣
- 30字以内
- 可以加入适当的emoji表情"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"我刚刚{activity_type}，帮我写个动态吧！"}
            ]
            
            response = await self.chat_completion(
                messages=messages,
                model=model,
                max_tokens=100,
                temperature=1.0
            )
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 生成动态内容失败: {e}")
            return f"{pet_name}今天{activity_type}啦！心情{mood}～ 🐾"
    
    async def analyze_sentiment(
        self,
        text: str,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分析文本情感
        
        Args:
            text: 要分析的文本
            model: 使用的模型
        
        Returns:
            情感分析结果
        """
        try:
            system_prompt = """你是一个情感分析专家，请分析用户输入的情感倾向。
返回JSON格式：
{
    "sentiment": "positive/negative/neutral",
    "confidence": 0.95,
    "emotions": ["开心", "兴奋"],
    "mood_score": 8
}"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"请分析这段话的情感：{text}"}
            ]
            
            response = await self.chat_completion(
                messages=messages,
                model=model,
                max_tokens=200,
                temperature=0.3
            )
            
            if response:
                import json
                try:
                    return json.loads(response)
                except json.JSONDecodeError:
                    logger.warning("情感分析返回格式不正确")
            
            # 默认返回
            return {
                "sentiment": "neutral",
                "confidence": 0.5,
                "emotions": ["平静"],
                "mood_score": 5
            }
            
        except Exception as e:
            logger.error(f"❌ 情感分析失败: {e}")
            return {
                "sentiment": "neutral",
                "confidence": 0.0,
                "emotions": ["未知"],
                "mood_score": 5
            }


# 创建全局AI客户端实例
ai_client = OpenRouterClient()


# 便捷函数
async def chat_with_pet(
    user_message: str,
    pet_name: str,
    pet_personality: str,
    pet_breed: str,
    history: List[Dict[str, str]] = None
) -> str:
    """与宠物聊天的便捷函数"""
    response = await ai_client.generate_pet_response(
        user_message=user_message,
        pet_name=pet_name,
        pet_personality=pet_personality,
        pet_breed=pet_breed,
        conversation_history=history
    )
    return response or f"汪汪！{pet_name}现在有点害羞，不知道说什么好～"


async def generate_pet_post(
    pet_name: str,
    pet_personality: str,
    activity: str,
    mood: str = "开心"
) -> str:
    """生成宠物动态的便捷函数"""
    response = await ai_client.generate_feed_content(
        pet_name=pet_name,
        pet_personality=pet_personality,
        activity_type=activity,
        mood=mood
    )
    return response or f"{pet_name}今天{activity}啦！🐾" 