"""
动态分享数据模型
包含Feed、FeedLike、FeedComment等模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON, Float, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum


class FeedStatus(str, enum.Enum):
    """动态状态枚举"""
    DRAFT = "draft"          # 草稿
    PUBLISHED = "published"  # 已发布
    HIDDEN = "hidden"        # 隐藏
    DELETED = "deleted"      # 已删除


class Feed(Base):
    """动态分享模型"""
    __tablename__ = "feeds"

    id = Column(Integer, primary_key=True, index=True)
    pet_id = Column(Integer, ForeignKey("pets.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    # 内容信息
    content = Column(Text, nullable=False, comment="动态内容")
    images = Column(JSON, nullable=True, comment="图片URL列表")
    mood = Column(String(50), nullable=True, comment="心情标签")
    tags = Column(JSON, nullable=True, comment="话题标签列表")
    location = Column(String(200), nullable=True, comment="位置信息")

    # 统计数据
    likes_count = Column(Integer, default=0, comment="点赞数")
    comments_count = Column(Integer, default=0, comment="评论数")
    shares_count = Column(Integer, default=0, comment="分享数")
    views_count = Column(Integer, default=0, comment="浏览数")

    # AI生成相关
    ai_generated_content = Column(Text, nullable=True, comment="AI生成的文案")
    ai_mood_score = Column(Float, nullable=True, comment="AI情感分析得分")
    ai_quality_score = Column(Float, nullable=True, comment="AI内容质量评分")
    ai_tags = Column(JSON, nullable=True, comment="AI自动识别的标签")

    # 状态和权限
    status = Column(String(20), default=FeedStatus.PUBLISHED, comment="动态状态")
    is_public = Column(Boolean, default=True, comment="是否公开")
    is_featured = Column(Boolean, default=False, comment="是否精选")
    is_ai_generated = Column(Boolean, default=False, comment="是否AI生成")

    # 时间字段
    published_at = Column(DateTime(timezone=True), nullable=True, comment="发布时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系定义
    pet = relationship("Pet", back_populates="feeds")
    user = relationship("User", back_populates="feeds")
    likes = relationship("FeedLike", back_populates="feed", cascade="all, delete-orphan")
    comments = relationship("FeedComment", back_populates="feed", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_feed_user_created', 'user_id', 'created_at'),
        Index('idx_feed_pet_created', 'pet_id', 'created_at'),
        Index('idx_feed_status_public', 'status', 'is_public'),
        Index('idx_feed_featured', 'is_featured', 'created_at'),
    )

    def __repr__(self):
        return f"<Feed(id={self.id}, pet_id={self.pet_id}, user_id={self.user_id})>"

    @property
    def image_urls(self) -> list:
        """获取图片URL列表"""
        return self.images or []

    @image_urls.setter
    def image_urls(self, urls: list):
        """设置图片URL列表"""
        self.images = urls if urls else None

    @property
    def tag_list(self) -> list:
        """获取标签列表"""
        return self.tags or []

    @tag_list.setter
    def tag_list(self, tags: list):
        """设置标签列表"""
        self.tags = tags if tags else None

    def increment_views(self):
        """增加浏览数"""
        self.views_count += 1

    def increment_likes(self):
        """增加点赞数"""
        self.likes_count += 1

    def decrement_likes(self):
        """减少点赞数"""
        if self.likes_count > 0:
            self.likes_count -= 1

    def increment_comments(self):
        """增加评论数"""
        self.comments_count += 1

    def decrement_comments(self):
        """减少评论数"""
        if self.comments_count > 0:
            self.comments_count -= 1

    def increment_shares(self):
        """增加分享数"""
        self.shares_count += 1


class FeedLike(Base):
    """动态点赞模型"""
    __tablename__ = "feed_likes"

    id = Column(Integer, primary_key=True, index=True)
    feed_id = Column(Integer, ForeignKey("feeds.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="点赞时间")

    # 关系定义
    feed = relationship("Feed", back_populates="likes")
    user = relationship("User")

    # 唯一约束：一个用户只能对一个动态点赞一次
    __table_args__ = (
        Index('idx_feed_like_unique', 'feed_id', 'user_id', unique=True),
        Index('idx_feed_like_user', 'user_id', 'created_at'),
    )

    def __repr__(self):
        return f"<FeedLike(id={self.id}, feed_id={self.feed_id}, user_id={self.user_id})>"


class FeedComment(Base):
    """动态评论模型"""
    __tablename__ = "feed_comments"

    id = Column(Integer, primary_key=True, index=True)
    feed_id = Column(Integer, ForeignKey("feeds.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    parent_id = Column(Integer, ForeignKey("feed_comments.id"), nullable=True, index=True)  # 回复评论的父评论ID

    # 评论内容
    content = Column(Text, nullable=False, comment="评论内容")

    # 状态
    is_deleted = Column(Boolean, default=False, comment="是否已删除")
    is_hidden = Column(Boolean, default=False, comment="是否隐藏")

    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="评论时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系定义
    feed = relationship("Feed", back_populates="comments")
    user = relationship("User")
    parent = relationship("FeedComment", remote_side=[id], backref="replies")

    # 索引
    __table_args__ = (
        Index('idx_feed_comment_feed', 'feed_id', 'created_at'),
        Index('idx_feed_comment_user', 'user_id', 'created_at'),
        Index('idx_feed_comment_parent', 'parent_id', 'created_at'),
    )

    def __repr__(self):
        return f"<FeedComment(id={self.id}, feed_id={self.feed_id}, user_id={self.user_id})>"

    @property
    def is_reply(self) -> bool:
        """是否为回复评论"""
        return self.parent_id is not None

    @property
    def reply_count(self) -> int:
        """回复数量"""
        return len([reply for reply in self.replies if not reply.is_deleted])