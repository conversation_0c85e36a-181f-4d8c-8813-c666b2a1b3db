"""
对话会话数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Conversation(Base):
    """对话会话模型 - 管理用户与宠物的对话会话"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    pet_id = Column(Integer, ForeignKey("pets.id"), nullable=False)
    
    # 会话信息
    title = Column(String(200), nullable=True)  # 会话标题（可选）
    summary = Column(Text, nullable=True)  # 会话摘要
    
    # 统计信息
    message_count = Column(Integer, default=0)  # 消息总数
    total_tokens = Column(Integer, default=0)  # 总token使用量
    
    # 会话状态
    is_active = Column(Boolean, default=True)  # 是否活跃
    last_message_at = Column(DateTime(timezone=True), nullable=True)  # 最后消息时间
    
    # 上下文管理
    context_window = Column(Integer, default=10)  # 上下文窗口大小
    context_data = Column(JSON, nullable=True)  # 上下文数据缓存
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="conversations")
    pet = relationship("Pet", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, user_id={self.user_id}, pet_id={self.pet_id}, messages={self.message_count})>"
