"""
消息数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Enum, Float, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum


class MessageType(enum.Enum):
    """消息类型枚举"""
    USER = "user"
    PET = "pet"
    SYSTEM = "system"


class Message(Base):
    """消息模型 - 存储对话中的每条消息"""
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    pet_id = Column(Integer, ForeignKey("pets.id"), nullable=False)
    message_type = Column(Enum(MessageType), nullable=False)
    content = Column(Text, nullable=False)  # 消息内容

    # AI相关字段
    ai_model = Column(String(100), nullable=True)  # 使用的AI模型
    ai_prompt = Column(Text, nullable=True)  # AI提示词
    tokens_used = Column(Integer, nullable=True)  # 使用的token数量
    response_time = Column(Float, nullable=True)  # AI响应时间(秒)
    confidence_score = Column(Float, nullable=True)  # AI回复置信度

    # 情感和行为
    mood = Column(String(50), nullable=True)  # 消息情绪
    actions = Column(JSON, nullable=True)  # 宠物行为动作列表
    emotions = Column(JSON, nullable=True)  # 情感标签列表

    # 元数据
    extra_data = Column(JSON, nullable=True)  # 其他附加信息
    is_read = Column(Boolean, default=True)  # 是否已读
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="messages")
    pet = relationship("Pet", back_populates="messages")
    conversation = relationship("Conversation", back_populates="messages")