"""
AI对话相关API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import asyncio

from app.core.database import get_db
from app.models.user import User
from app.models.pet import Pet
from app.models.message import MessageType
from app.services.conversation_service import ConversationService
from app.services.prompt_service import prompt_service
from app.services.cache_service import cache_service, token_tracker
from app.utils.ai_client import ai_client
from app.api.v1.auth import get_current_user

router = APIRouter(prefix="/ai", tags=["AI对话"])


# Pydantic模型
class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息", min_length=1, max_length=1000)
    pet_id: int = Field(..., description="宠物ID")
    context_data: Optional[Dict[str, Any]] = Field(None, description="上下文数据")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    reply: str = Field(..., description="AI回复内容")
    mood: str = Field(..., description="宠物心情")
    actions: List[str] = Field(default_factory=list, description="宠物行为动作")
    emotions: List[str] = Field(default_factory=list, description="情感标签")
    confidence: float = Field(..., description="回复置信度")
    conversation_id: int = Field(..., description="对话ID")
    message_id: int = Field(..., description="消息ID")
    response_time: float = Field(..., description="响应时间")
    tokens_used: int = Field(..., description="使用的token数量")


class ConversationHistoryResponse(BaseModel):
    """对话历史响应模型"""
    conversation_id: int = Field(..., description="对话ID")
    messages: List[Dict[str, Any]] = Field(..., description="消息列表")
    total_count: int = Field(..., description="消息总数")
    has_more: bool = Field(..., description="是否有更多消息")


class ConversationSummaryResponse(BaseModel):
    """对话摘要响应模型"""
    conversation_id: int = Field(..., description="对话ID")
    pet_name: str = Field(..., description="宠物名称")
    message_count: int = Field(..., description="消息数量")
    total_tokens: int = Field(..., description="总token使用量")
    created_at: str = Field(..., description="创建时间")
    last_message_at: Optional[str] = Field(None, description="最后消息时间")
    is_active: bool = Field(..., description="是否活跃")


@router.get("/health")
async def ai_health():
    """AI服务健康检查"""
    return {"status": "ok", "service": "ai"}


@router.post("/chat", response_model=ChatResponse)
async def chat_with_pet(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    与宠物聊天

    发送消息给指定宠物，获得AI生成的个性化回复
    """
    try:
        # 验证宠物所有权
        pet = await db.get(Pet, request.pet_id)
        if not pet or pet.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 获取或创建对话
        conv_service = ConversationService(db)
        conversation = await conv_service.get_or_create_conversation(
            user_id=current_user.id,
            pet_id=request.pet_id
        )

        # 保存用户消息
        user_message = await conv_service.add_message(
            conversation_id=conversation.id,
            user_id=current_user.id,
            pet_id=request.pet_id,
            content=request.message,
            message_type=MessageType.USER
        )

        # 获取对话上下文
        context_messages = await conv_service.get_context_messages(
            conversation_id=conversation.id,
            context_window=conversation.context_window
        )

        # 构建宠物数据
        pet_data = {
            "name": pet.name,
            "breed": pet.breed,
            "personality": pet.personality,
            "personality_tags": pet.personality_tags,
            "current_mood": pet.current_mood,
            "age": pet.age,
            "response_style": pet.response_style
        }

        # 生成AI回复（使用缓存优化）
        ai_response = await ai_client.generate_enhanced_pet_response(
            user_message=request.message,
            pet_data=pet_data,
            conversation_history=context_messages,
            context_data=request.context_data,
            user_id=current_user.id,
            pet_id=request.pet_id,
            use_cache=True
        )

        # 保存AI回复
        ai_message = await conv_service.add_message(
            conversation_id=conversation.id,
            user_id=current_user.id,
            pet_id=request.pet_id,
            content=ai_response["content"],
            message_type=MessageType.PET,
            ai_data=ai_response
        )

        # 更新宠物互动统计
        pet.add_interaction()
        await db.commit()

        return ChatResponse(
            reply=ai_response["content"],
            mood=ai_response["mood"],
            actions=ai_response["actions"],
            emotions=ai_response["emotions"],
            confidence=ai_response["confidence"],
            conversation_id=conversation.id,
            message_id=ai_message.id,
            response_time=ai_response["response_time"],
            tokens_used=ai_response["tokens_used"]
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"聊天失败: {str(e)}"
        )


@router.get("/conversations/{pet_id}/history", response_model=ConversationHistoryResponse)
async def get_conversation_history(
    pet_id: int,
    limit: int = 20,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取与指定宠物的对话历史
    """
    try:
        # 验证宠物所有权
        pet = await db.get(Pet, pet_id)
        if not pet or pet.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 获取对话
        conv_service = ConversationService(db)
        conversation = await conv_service.get_or_create_conversation(
            user_id=current_user.id,
            pet_id=pet_id
        )

        # 获取消息历史
        messages = await conv_service.get_conversation_history(
            conversation_id=conversation.id,
            limit=limit,
            offset=offset
        )

        # 格式化消息
        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                "id": msg.id,
                "content": msg.content,
                "type": msg.message_type.value,
                "mood": msg.mood,
                "actions": msg.actions,
                "emotions": msg.emotions,
                "created_at": msg.created_at.isoformat(),
                "confidence": msg.confidence_score,
                "tokens_used": msg.tokens_used
            })

        return ConversationHistoryResponse(
            conversation_id=conversation.id,
            messages=formatted_messages,
            total_count=conversation.message_count,
            has_more=len(messages) == limit
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话历史失败: {str(e)}"
        )


@router.get("/conversations/{pet_id}/summary", response_model=ConversationSummaryResponse)
async def get_conversation_summary(
    pet_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取与指定宠物的对话摘要
    """
    try:
        # 验证宠物所有权
        pet = await db.get(Pet, pet_id)
        if not pet or pet.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 获取对话
        conv_service = ConversationService(db)
        conversation = await conv_service.get_or_create_conversation(
            user_id=current_user.id,
            pet_id=pet_id
        )

        # 获取对话摘要
        summary = await conv_service.get_conversation_summary(conversation.id)

        return ConversationSummaryResponse(
            conversation_id=conversation.id,
            pet_name=summary["pet_name"],
            message_count=summary["message_count"],
            total_tokens=summary["total_tokens"],
            created_at=summary["created_at"].isoformat(),
            last_message_at=summary["last_message_at"].isoformat() if summary["last_message_at"] else None,
            is_active=summary["is_active"]
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话摘要失败: {str(e)}"
        )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除对话（归档）
    """
    try:
        conv_service = ConversationService(db)

        # 验证对话所有权
        summary = await conv_service.get_conversation_summary(conversation_id)
        if not summary or summary.get("user_name") != current_user.username:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话不存在或无权限访问"
            )

        # 归档对话
        await conv_service.archive_conversation(
            conversation_id=conversation_id,
            summary="用户主动删除"
        )

        return {"message": "对话已删除", "conversation_id": conversation_id}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除对话失败: {str(e)}"
        )


@router.get("/conversations")
async def get_user_conversations(
    pet_id: Optional[int] = None,
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户的对话列表
    """
    try:
        conv_service = ConversationService(db)
        conversations = await conv_service.get_user_conversations(
            user_id=current_user.id,
            pet_id=pet_id,
            limit=limit
        )

        return {
            "conversations": conversations,
            "total": len(conversations)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话列表失败: {str(e)}"
        )


@router.post("/generate-starter/{pet_id}")
async def generate_conversation_starter(
    pet_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    生成对话开场白
    """
    try:
        # 验证宠物所有权
        pet = await db.get(Pet, pet_id)
        if not pet or pet.owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="宠物不存在或无权限访问"
            )

        # 生成开场白
        starter = prompt_service.generate_conversation_starter(pet)

        return {
            "starter": starter,
            "pet_name": pet.name,
            "pet_mood": pet.current_mood.value
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成开场白失败: {str(e)}"
        )


@router.get("/stats/cache")
async def get_cache_stats(
    current_user: User = Depends(get_current_user)
):
    """
    获取缓存统计信息
    """
    try:
        stats = cache_service.get_stats()
        return {
            "cache_stats": stats,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取缓存统计失败: {str(e)}"
        )


@router.get("/stats/tokens")
async def get_token_usage_stats(
    days: int = 7,
    current_user: User = Depends(get_current_user)
):
    """
    获取Token使用统计
    """
    try:
        usage_summary = await token_tracker.get_usage_summary(
            user_id=current_user.id,
            days=days
        )

        return {
            "usage_summary": usage_summary,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Token统计失败: {str(e)}"
        )