"""
应用监控和指标采集系统
提供性能监控、健康检查、指标采集等功能
"""

import time
import psutil
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from loguru import logger
import json


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: float
    value: float
    tags: Dict[str, str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_points: int = 1000):
        self.max_points = max_points
        self.metrics = defaultdict(lambda: deque(maxlen=max_points))
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.histograms = defaultdict(list)
        
        # 系统指标
        self.system_metrics = {
            "cpu_percent": 0.0,
            "memory_percent": 0.0,
            "disk_usage_percent": 0.0,
            "network_io": {"bytes_sent": 0, "bytes_recv": 0},
            "process_count": 0
        }
        
        # 应用指标
        self.app_metrics = {
            "requests_total": 0,
            "requests_per_second": 0.0,
            "response_time_avg": 0.0,
            "error_rate": 0.0,
            "active_connections": 0,
            "database_connections": 0
        }
        
        # 业务指标
        self.business_metrics = {
            "users_online": 0,
            "feeds_created_today": 0,
            "ai_requests_today": 0,
            "cache_hit_rate": 0.0
        }
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录指标"""
        point = MetricPoint(
            timestamp=time.time(),
            value=value,
            tags=tags or {}
        )
        self.metrics[name].append(point)
    
    def increment_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """递增计数器"""
        self.counters[name] += value
        self.record_metric(f"{name}_total", self.counters[name], tags)
    
    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """设置仪表盘值"""
        self.gauges[name] = value
        self.record_metric(name, value, tags)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录直方图"""
        self.histograms[name].append(value)
        # 保持最近1000个值
        if len(self.histograms[name]) > 1000:
            self.histograms[name] = self.histograms[name][-1000:]
        
        # 计算统计值
        values = self.histograms[name]
        if values:
            avg = sum(values) / len(values)
            self.record_metric(f"{name}_avg", avg, tags)
            self.record_metric(f"{name}_min", min(values), tags)
            self.record_metric(f"{name}_max", max(values), tags)
            
            # 计算百分位数
            sorted_values = sorted(values)
            p50 = sorted_values[int(len(sorted_values) * 0.5)]
            p95 = sorted_values[int(len(sorted_values) * 0.95)]
            p99 = sorted_values[int(len(sorted_values) * 0.99)]
            
            self.record_metric(f"{name}_p50", p50, tags)
            self.record_metric(f"{name}_p95", p95, tags)
            self.record_metric(f"{name}_p99", p99, tags)
    
    async def collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.system_metrics["cpu_percent"] = cpu_percent
            self.set_gauge("system_cpu_percent", cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.system_metrics["memory_percent"] = memory_percent
            self.set_gauge("system_memory_percent", memory_percent)
            self.set_gauge("system_memory_used_bytes", memory.used)
            self.set_gauge("system_memory_available_bytes", memory.available)
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.system_metrics["disk_usage_percent"] = disk_percent
            self.set_gauge("system_disk_percent", disk_percent)
            self.set_gauge("system_disk_used_bytes", disk.used)
            self.set_gauge("system_disk_free_bytes", disk.free)
            
            # 网络IO
            network = psutil.net_io_counters()
            self.system_metrics["network_io"] = {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv
            }
            self.set_gauge("system_network_bytes_sent", network.bytes_sent)
            self.set_gauge("system_network_bytes_recv", network.bytes_recv)
            
            # 进程数
            process_count = len(psutil.pids())
            self.system_metrics["process_count"] = process_count
            self.set_gauge("system_process_count", process_count)
            
        except Exception as e:
            logger.error(f"系统指标收集失败: {e}")
    
    def get_metrics_summary(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """获取指标摘要"""
        cutoff_time = time.time() - (duration_minutes * 60)
        summary = {}
        
        for metric_name, points in self.metrics.items():
            recent_points = [p for p in points if p.timestamp >= cutoff_time]
            if recent_points:
                values = [p.value for p in recent_points]
                summary[metric_name] = {
                    "count": len(values),
                    "avg": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "latest": values[-1] if values else 0
                }
        
        return {
            "summary": summary,
            "system": self.system_metrics,
            "application": self.app_metrics,
            "business": self.business_metrics,
            "counters": dict(self.counters),
            "gauges": dict(self.gauges)
        }


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.checks = {}
        self.last_check_time = {}
        self.check_results = {}
    
    def register_check(self, name: str, check_func, interval: int = 60):
        """注册健康检查"""
        self.checks[name] = {
            "func": check_func,
            "interval": interval
        }
        self.last_check_time[name] = 0
        self.check_results[name] = {"status": "unknown", "message": "未检查"}
    
    async def run_check(self, name: str) -> Dict[str, Any]:
        """运行单个健康检查"""
        if name not in self.checks:
            return {"status": "error", "message": "检查不存在"}
        
        try:
            check_func = self.checks[name]["func"]
            start_time = time.time()
            
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            
            duration = time.time() - start_time
            
            if isinstance(result, bool):
                status = "healthy" if result else "unhealthy"
                message = "检查通过" if result else "检查失败"
            elif isinstance(result, dict):
                status = result.get("status", "unknown")
                message = result.get("message", "无消息")
            else:
                status = "healthy" if result else "unhealthy"
                message = str(result)
            
            self.check_results[name] = {
                "status": status,
                "message": message,
                "duration": duration,
                "timestamp": time.time()
            }
            
            return self.check_results[name]
            
        except Exception as e:
            error_result = {
                "status": "error",
                "message": f"检查异常: {str(e)}",
                "timestamp": time.time()
            }
            self.check_results[name] = error_result
            return error_result
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        current_time = time.time()
        results = {}
        
        for name, check_config in self.checks.items():
            # 检查是否需要运行
            if current_time - self.last_check_time[name] >= check_config["interval"]:
                results[name] = await self.run_check(name)
                self.last_check_time[name] = current_time
            else:
                # 使用缓存结果
                results[name] = self.check_results.get(name, {
                    "status": "unknown",
                    "message": "未检查"
                })
        
        # 计算整体健康状态
        overall_status = "healthy"
        unhealthy_checks = []
        
        for name, result in results.items():
            if result["status"] in ["unhealthy", "error"]:
                overall_status = "unhealthy"
                unhealthy_checks.append(name)
        
        return {
            "overall_status": overall_status,
            "checks": results,
            "unhealthy_checks": unhealthy_checks,
            "timestamp": current_time
        }


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.request_times = deque(maxlen=1000)
        self.error_count = 0
        self.total_requests = 0
        self.start_time = time.time()
    
    def record_request(self, duration: float, status_code: int):
        """记录请求"""
        self.request_times.append(duration)
        self.total_requests += 1
        
        if status_code >= 400:
            self.error_count += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.request_times:
            return {
                "avg_response_time": 0,
                "min_response_time": 0,
                "max_response_time": 0,
                "error_rate": 0,
                "requests_per_second": 0,
                "total_requests": self.total_requests
            }
        
        times = list(self.request_times)
        uptime = time.time() - self.start_time
        
        return {
            "avg_response_time": sum(times) / len(times),
            "min_response_time": min(times),
            "max_response_time": max(times),
            "error_rate": self.error_count / self.total_requests if self.total_requests > 0 else 0,
            "requests_per_second": self.total_requests / uptime if uptime > 0 else 0,
            "total_requests": self.total_requests,
            "uptime_seconds": uptime
        }


# 全局实例
metrics_collector = MetricsCollector()
health_checker = HealthChecker()
performance_monitor = PerformanceMonitor()


# 注册基础健康检查
async def database_health_check():
    """数据库健康检查"""
    try:
        from app.core.database import db_manager
        return await db_manager.health_check()
    except Exception as e:
        return {"status": "unhealthy", "message": f"数据库连接失败: {e}"}


async def redis_health_check():
    """Redis健康检查"""
    try:
        from app.services.redis_cache_service import cache_service
        return await cache_service.health_check()
    except Exception as e:
        return {"status": "unhealthy", "message": f"Redis连接失败: {e}"}


def disk_space_check():
    """磁盘空间检查"""
    try:
        disk = psutil.disk_usage('/')
        free_percent = (disk.free / disk.total) * 100
        
        if free_percent < 10:
            return {"status": "unhealthy", "message": f"磁盘空间不足: {free_percent:.1f}%"}
        elif free_percent < 20:
            return {"status": "warning", "message": f"磁盘空间较低: {free_percent:.1f}%"}
        else:
            return {"status": "healthy", "message": f"磁盘空间充足: {free_percent:.1f}%"}
    except Exception as e:
        return {"status": "error", "message": f"磁盘检查失败: {e}"}


# 注册健康检查
health_checker.register_check("database", database_health_check, interval=30)
health_checker.register_check("redis", redis_health_check, interval=30)
health_checker.register_check("disk_space", disk_space_check, interval=60)
