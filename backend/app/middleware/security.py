"""
安全防护中间件
提供输入验证、SQL注入防护、XSS攻击防护、敏感信息脱敏等安全功能
"""

import re
import html
import json
from typing import Any, Dict, List, Optional, Union
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger
import hashlib
import secrets


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全防护中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.sql_injection_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
            r"(--|#|/\*|\*/)",
            r"(\bUNION\s+SELECT\b)",
            r"(\b(SLEEP|BENCHMARK|WAITFOR)\s*\()",
            r"(\b(LOAD_FILE|INTO\s+OUTFILE)\b)",
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>",
            r"<object[^>]*>.*?</object>",
            r"<embed[^>]*>.*?</embed>",
            r"<link[^>]*>",
            r"<meta[^>]*>",
        ]
        
        self.sensitive_fields = {
            "password", "token", "secret", "key", "auth", "credential",
            "hashed_password", "access_token", "refresh_token", "api_key"
        }
        
        self.blocked_user_agents = [
            "sqlmap", "nikto", "nmap", "masscan", "zap", "burp"
        ]
    
    async def dispatch(self, request: Request, call_next):
        """处理请求安全检查"""
        try:
            # 1. 检查User-Agent
            if self._is_blocked_user_agent(request):
                logger.warning(f"阻止可疑User-Agent: {request.headers.get('user-agent')}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
            
            # 2. 检查请求大小
            if self._is_request_too_large(request):
                logger.warning(f"请求过大: {request.url.path}")
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail="Request too large"
                )
            
            # 3. 检查路径遍历攻击
            if self._has_path_traversal(request):
                logger.warning(f"路径遍历攻击: {request.url.path}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid path"
                )
            
            # 4. 处理请求体安全检查
            if request.method in ["POST", "PUT", "PATCH"]:
                request = await self._sanitize_request_body(request)
            
            # 5. 处理请求
            response = await call_next(request)
            
            # 6. 处理响应安全
            response = self._add_security_headers(response)
            response = await self._sanitize_response(response)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"安全中间件异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Security check failed"
            )
    
    def _is_blocked_user_agent(self, request: Request) -> bool:
        """检查是否为被阻止的User-Agent"""
        user_agent = request.headers.get("user-agent", "").lower()
        return any(blocked in user_agent for blocked in self.blocked_user_agents)
    
    def _is_request_too_large(self, request: Request) -> bool:
        """检查请求是否过大"""
        content_length = request.headers.get("content-length")
        if content_length:
            try:
                size = int(content_length)
                # 限制请求大小为10MB
                return size > 10 * 1024 * 1024
            except ValueError:
                return False
        return False
    
    def _has_path_traversal(self, request: Request) -> bool:
        """检查路径遍历攻击"""
        path = request.url.path
        dangerous_patterns = ["../", "..\\", "%2e%2e", "%2f", "%5c"]
        return any(pattern in path.lower() for pattern in dangerous_patterns)
    
    async def _sanitize_request_body(self, request: Request) -> Request:
        """清理请求体"""
        try:
            if request.headers.get("content-type", "").startswith("application/json"):
                body = await request.body()
                if body:
                    data = json.loads(body.decode())
                    sanitized_data = self._sanitize_data(data)
                    
                    # 重新设置请求体
                    request._body = json.dumps(sanitized_data).encode()
            
            return request
        except Exception as e:
            logger.error(f"请求体清理失败: {e}")
            return request
    
    def _sanitize_data(self, data: Any) -> Any:
        """递归清理数据"""
        if isinstance(data, dict):
            return {key: self._sanitize_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data]
        elif isinstance(data, str):
            return self._sanitize_string(data)
        else:
            return data
    
    def _sanitize_string(self, text: str) -> str:
        """清理字符串"""
        if not text:
            return text
        
        # 1. 检查SQL注入
        if self._has_sql_injection(text):
            logger.warning(f"检测到SQL注入尝试: {text[:100]}")
            # 移除危险字符
            for pattern in self.sql_injection_patterns:
                text = re.sub(pattern, "", text, flags=re.IGNORECASE)
        
        # 2. 检查XSS攻击
        if self._has_xss_attempt(text):
            logger.warning(f"检测到XSS攻击尝试: {text[:100]}")
            # HTML转义
            text = html.escape(text)
            # 移除危险标签
            for pattern in self.xss_patterns:
                text = re.sub(pattern, "", text, flags=re.IGNORECASE | re.DOTALL)
        
        # 3. 限制长度
        if len(text) > 10000:  # 限制单个字段最大10KB
            text = text[:10000]
        
        return text.strip()
    
    def _has_sql_injection(self, text: str) -> bool:
        """检查SQL注入"""
        text_lower = text.lower()
        return any(re.search(pattern, text_lower, re.IGNORECASE) for pattern in self.sql_injection_patterns)
    
    def _has_xss_attempt(self, text: str) -> bool:
        """检查XSS攻击"""
        text_lower = text.lower()
        return any(re.search(pattern, text_lower, re.IGNORECASE | re.DOTALL) for pattern in self.xss_patterns)
    
    def _add_security_headers(self, response: Response) -> Response:
        """添加安全头"""
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response
    
    async def _sanitize_response(self, response: Response) -> Response:
        """清理响应数据"""
        try:
            if hasattr(response, 'body') and response.headers.get("content-type", "").startswith("application/json"):
                body = response.body
                if body:
                    data = json.loads(body.decode())
                    sanitized_data = self._mask_sensitive_data(data)
                    response.body = json.dumps(sanitized_data).encode()
            
            return response
        except Exception as e:
            logger.error(f"响应清理失败: {e}")
            return response
    
    def _mask_sensitive_data(self, data: Any) -> Any:
        """脱敏敏感数据"""
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                if key.lower() in self.sensitive_fields:
                    # 脱敏处理
                    if isinstance(value, str) and value:
                        result[key] = "*" * min(len(value), 8)
                    else:
                        result[key] = "***"
                else:
                    result[key] = self._mask_sensitive_data(value)
            return result
        elif isinstance(data, list):
            return [self._mask_sensitive_data(item) for item in data]
        else:
            return data


class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_username(username: str) -> bool:
        """验证用户名格式"""
        # 3-20个字符，只允许字母、数字、下划线
        pattern = r'^[a-zA-Z0-9_]{3,20}$'
        return bool(re.match(pattern, username))
    
    @staticmethod
    def validate_password(password: str) -> Dict[str, Any]:
        """验证密码强度"""
        result = {
            "valid": True,
            "score": 0,
            "issues": []
        }
        
        if len(password) < 8:
            result["valid"] = False
            result["issues"].append("密码长度至少8位")
        else:
            result["score"] += 1
        
        if not re.search(r'[a-z]', password):
            result["issues"].append("需要包含小写字母")
        else:
            result["score"] += 1
        
        if not re.search(r'[A-Z]', password):
            result["issues"].append("需要包含大写字母")
        else:
            result["score"] += 1
        
        if not re.search(r'\d', password):
            result["issues"].append("需要包含数字")
        else:
            result["score"] += 1
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result["issues"].append("需要包含特殊字符")
        else:
            result["score"] += 1
        
        if result["issues"]:
            result["valid"] = False
        
        return result
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名"""
        # 移除危险字符
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        # 限制长度
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:255-len(ext)-1] + '.' + ext if ext else name[:255]
        return filename
    
    @staticmethod
    def validate_file_type(filename: str, allowed_types: List[str]) -> bool:
        """验证文件类型"""
        if not filename:
            return False
        
        ext = filename.lower().split('.')[-1] if '.' in filename else ''
        return ext in allowed_types


class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """生成安全令牌"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def hash_sensitive_data(data: str) -> str:
        """哈希敏感数据"""
        return hashlib.sha256(data.encode()).hexdigest()
    
    @staticmethod
    def mask_email(email: str) -> str:
        """脱敏邮箱"""
        if '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """脱敏手机号"""
        if len(phone) < 7:
            return '*' * len(phone)
        
        return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]


# 全局实例
input_validator = InputValidator()
security_utils = SecurityUtils()
