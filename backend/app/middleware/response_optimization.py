"""
API响应优化中间件
包含响应压缩、缓存控制、性能监控等功能
"""

import gzip
import json
import time
from typing import Callable, Dict, Any
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse
from loguru import logger
import asyncio


class ResponseOptimizationMiddleware(BaseHTTPMiddleware):
    """响应优化中间件"""
    
    def __init__(self, app, min_size: int = 1000):
        super().__init__(app)
        self.min_size = min_size  # 最小压缩大小
        self.performance_stats = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求和响应"""
        start_time = time.time()
        
        # 记录请求信息
        request_info = {
            "method": request.method,
            "url": str(request.url),
            "client_ip": request.client.host if request.client else "unknown",
            "user_agent": request.headers.get("user-agent", "unknown")
        }
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算响应时间
            process_time = time.time() - start_time
            
            # 添加性能头
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Timestamp"] = str(int(time.time()))
            
            # 响应压缩
            if self._should_compress(request, response):
                response = await self._compress_response(response)
            
            # 添加缓存控制头
            response = self._add_cache_headers(request, response)
            
            # 记录性能统计
            self._record_performance(request_info, response, process_time)
            
            # 记录慢请求
            if process_time > 2.0:
                logger.warning(
                    f"慢请求检测: {request.method} {request.url.path} "
                    f"耗时 {process_time:.2f}s"
                )
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(f"请求处理异常: {request_info} - {str(e)}")
            
            # 返回错误响应
            error_response = JSONResponse(
                status_code=500,
                content={"error": "Internal server error", "request_id": str(time.time())}
            )
            error_response.headers["X-Process-Time"] = str(process_time)
            return error_response
    
    def _should_compress(self, request: Request, response: Response) -> bool:
        """判断是否应该压缩响应"""
        # 检查Accept-Encoding头
        accept_encoding = request.headers.get("accept-encoding", "")
        if "gzip" not in accept_encoding:
            return False
        
        # 检查响应类型
        content_type = response.headers.get("content-type", "")
        if not content_type.startswith(("application/json", "text/")):
            return False
        
        # 检查响应大小
        content_length = response.headers.get("content-length")
        if content_length and int(content_length) < self.min_size:
            return False
        
        # 检查是否已经压缩
        if response.headers.get("content-encoding"):
            return False
        
        return True
    
    async def _compress_response(self, response: Response) -> Response:
        """压缩响应内容"""
        try:
            # 获取响应内容
            if hasattr(response, 'body'):
                content = response.body
            else:
                # 对于StreamingResponse等
                return response
            
            # 压缩内容
            compressed_content = gzip.compress(content)
            
            # 如果压缩后更大，则不压缩
            if len(compressed_content) >= len(content):
                return response
            
            # 创建新的响应
            compressed_response = Response(
                content=compressed_content,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
            
            # 添加压缩头
            compressed_response.headers["content-encoding"] = "gzip"
            compressed_response.headers["content-length"] = str(len(compressed_content))
            
            return compressed_response
            
        except Exception as e:
            logger.error(f"响应压缩失败: {e}")
            return response
    
    def _add_cache_headers(self, request: Request, response: Response) -> Response:
        """添加缓存控制头"""
        path = request.url.path
        
        # API路径缓存策略
        if path.startswith("/api/feeds/trending"):
            # 热门动态缓存5分钟
            response.headers["Cache-Control"] = "public, max-age=300"
        elif path.startswith("/api/feeds/") and request.method == "GET":
            # 动态列表缓存1分钟
            response.headers["Cache-Control"] = "public, max-age=60"
        elif path.startswith("/api/pets/") and request.method == "GET":
            # 宠物信息缓存10分钟
            response.headers["Cache-Control"] = "public, max-age=600"
        elif path.startswith("/api/users/") and request.method == "GET":
            # 用户信息缓存5分钟
            response.headers["Cache-Control"] = "public, max-age=300"
        else:
            # 默认不缓存
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        
        return response
    
    def _record_performance(
        self, 
        request_info: Dict[str, Any], 
        response: Response, 
        process_time: float
    ):
        """记录性能统计"""
        endpoint = f"{request_info['method']} {request_info['url']}"
        
        if endpoint not in self.performance_stats:
            self.performance_stats[endpoint] = {
                "count": 0,
                "total_time": 0,
                "min_time": float('inf'),
                "max_time": 0,
                "error_count": 0
            }
        
        stats = self.performance_stats[endpoint]
        stats["count"] += 1
        stats["total_time"] += process_time
        stats["min_time"] = min(stats["min_time"], process_time)
        stats["max_time"] = max(stats["max_time"], process_time)
        
        if response.status_code >= 400:
            stats["error_count"] += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats_summary = {}
        
        for endpoint, stats in self.performance_stats.items():
            if stats["count"] > 0:
                stats_summary[endpoint] = {
                    "count": stats["count"],
                    "avg_time": stats["total_time"] / stats["count"],
                    "min_time": stats["min_time"],
                    "max_time": stats["max_time"],
                    "error_rate": stats["error_count"] / stats["count"],
                    "total_time": stats["total_time"]
                }
        
        return stats_summary


class PaginationOptimizer:
    """分页查询优化器"""
    
    @staticmethod
    def optimize_pagination(
        query,
        page: int = 1,
        size: int = 20,
        max_size: int = 100
    ) -> Dict[str, Any]:
        """优化分页查询"""
        # 限制分页大小
        size = min(size, max_size)
        
        # 计算偏移量
        offset = (page - 1) * size
        
        # 获取总数（优化：只在第一页时计算）
        total = None
        if page == 1:
            total = query.count()
        
        # 执行分页查询
        items = query.offset(offset).limit(size).all()
        
        # 判断是否有更多数据
        has_more = len(items) == size
        
        return {
            "items": items,
            "page": page,
            "size": size,
            "total": total,
            "has_more": has_more
        }


class ConcurrencyOptimizer:
    """并发处理优化器"""
    
    def __init__(self, max_concurrent: int = 100):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_requests = 0
    
    async def limit_concurrency(self, func: Callable, *args, **kwargs):
        """限制并发数量"""
        async with self.semaphore:
            self.active_requests += 1
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                self.active_requests -= 1
    
    def get_concurrency_stats(self) -> Dict[str, Any]:
        """获取并发统计"""
        return {
            "active_requests": self.active_requests,
            "available_slots": self.semaphore._value,
            "max_concurrent": self.semaphore._bound_value
        }


class ResponseFormatter:
    """响应格式化器"""
    
    @staticmethod
    def format_api_response(
        data: Any = None,
        message: str = "success",
        code: int = 200,
        meta: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """标准化API响应格式"""
        response = {
            "code": code,
            "message": message,
            "timestamp": int(time.time())
        }
        
        if data is not None:
            response["data"] = data
        
        if meta:
            response["meta"] = meta
        
        return response
    
    @staticmethod
    def format_error_response(
        error: str,
        code: int = 400,
        details: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """标准化错误响应格式"""
        response = {
            "code": code,
            "error": error,
            "timestamp": int(time.time())
        }
        
        if details:
            response["details"] = details
        
        return response
    
    @staticmethod
    def format_list_response(
        items: list,
        page: int = 1,
        size: int = 20,
        total: int = None,
        has_more: bool = False
    ) -> Dict[str, Any]:
        """标准化列表响应格式"""
        return {
            "code": 200,
            "message": "success",
            "data": {
                "items": items,
                "pagination": {
                    "page": page,
                    "size": size,
                    "total": total,
                    "has_more": has_more
                }
            },
            "timestamp": int(time.time())
        }


# 全局实例
response_optimizer = ResponseOptimizationMiddleware
pagination_optimizer = PaginationOptimizer()
concurrency_optimizer = ConcurrencyOptimizer()
response_formatter = ResponseFormatter()
