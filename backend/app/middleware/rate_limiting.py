"""
API限流中间件
提供灵活的API访问频率限制功能
"""

import time
from typing import Dict, Any, Optional, Callable
from fastapi import Request, HTTPException, status
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from loguru import logger
import asyncio
from collections import defaultdict, deque


class AdvancedRateLimiter:
    """高级限流器"""
    
    def __init__(self):
        self.request_history = defaultdict(deque)
        self.blocked_ips = {}
        self.whitelist = set()
        self.blacklist = set()
        
        # 限流配置
        self.rate_limits = {
            # 通用API限制
            "default": {"requests": 100, "window": 60},  # 每分钟100次
            
            # 认证相关限制
            "auth_login": {"requests": 5, "window": 60},  # 登录每分钟5次
            "auth_register": {"requests": 3, "window": 300},  # 注册每5分钟3次
            "auth_reset_password": {"requests": 2, "window": 300},  # 重置密码每5分钟2次
            
            # AI相关限制
            "ai_chat": {"requests": 30, "window": 60},  # AI对话每分钟30次
            "ai_generate": {"requests": 10, "window": 60},  # AI生成每分钟10次
            
            # 动态相关限制
            "feeds_create": {"requests": 20, "window": 60},  # 发布动态每分钟20次
            "feeds_like": {"requests": 100, "window": 60},  # 点赞每分钟100次
            "feeds_comment": {"requests": 50, "window": 60},  # 评论每分钟50次
            
            # 上传相关限制
            "upload": {"requests": 10, "window": 60},  # 上传每分钟10次
            
            # 管理员限制
            "admin": {"requests": 1000, "window": 60},  # 管理员每分钟1000次
        }
    
    def get_client_identifier(self, request: Request) -> str:
        """获取客户端标识符"""
        # 优先使用用户ID（如果已认证）
        if hasattr(request.state, 'user') and request.state.user:
            return f"user:{request.state.user.id}"
        
        # 使用IP地址
        client_ip = get_remote_address(request)
        
        # 检查X-Forwarded-For头（代理情况）
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
    
    def get_rate_limit_key(self, request: Request) -> str:
        """获取限流键"""
        path = request.url.path
        method = request.method
        
        # 根据路径确定限流类型
        if path.startswith("/auth/login"):
            return "auth_login"
        elif path.startswith("/auth/register"):
            return "auth_register"
        elif path.startswith("/auth/reset-password"):
            return "auth_reset_password"
        elif path.startswith("/api/ai/chat"):
            return "ai_chat"
        elif path.startswith("/api/feeds/ai/generate"):
            return "ai_generate"
        elif path.startswith("/api/feeds/") and method == "POST":
            return "feeds_create"
        elif path.endswith("/like") and method == "POST":
            return "feeds_like"
        elif path.endswith("/comments") and method == "POST":
            return "feeds_comment"
        elif "upload" in path:
            return "upload"
        elif path.startswith("/admin/"):
            return "admin"
        else:
            return "default"
    
    def is_rate_limited(self, request: Request) -> tuple[bool, Dict[str, Any]]:
        """检查是否被限流"""
        client_id = self.get_client_identifier(request)
        rate_key = self.get_rate_limit_key(request)
        
        # 检查白名单
        if client_id in self.whitelist:
            return False, {}
        
        # 检查黑名单
        if client_id in self.blacklist:
            return True, {"reason": "blacklisted", "retry_after": 3600}
        
        # 检查是否被临时封禁
        if client_id in self.blocked_ips:
            block_info = self.blocked_ips[client_id]
            if time.time() < block_info["until"]:
                return True, {
                    "reason": "temporarily_blocked",
                    "retry_after": int(block_info["until"] - time.time())
                }
            else:
                # 解除封禁
                del self.blocked_ips[client_id]
        
        # 获取限流配置
        limit_config = self.rate_limits.get(rate_key, self.rate_limits["default"])
        max_requests = limit_config["requests"]
        window_seconds = limit_config["window"]
        
        # 检查请求历史
        now = time.time()
        history_key = f"{client_id}:{rate_key}"
        request_times = self.request_history[history_key]
        
        # 清理过期记录
        while request_times and request_times[0] <= now - window_seconds:
            request_times.popleft()
        
        # 检查是否超过限制
        if len(request_times) >= max_requests:
            # 检查是否需要临时封禁（连续超限）
            if len(request_times) >= max_requests * 2:
                self.blocked_ips[client_id] = {
                    "until": now + 300,  # 封禁5分钟
                    "reason": "excessive_requests"
                }
                logger.warning(f"临时封禁客户端: {client_id}")
            
            retry_after = int(window_seconds - (now - request_times[0]))
            return True, {
                "reason": "rate_limited",
                "retry_after": retry_after,
                "limit": max_requests,
                "window": window_seconds
            }
        
        # 记录请求
        request_times.append(now)
        
        return False, {}
    
    def add_to_whitelist(self, identifier: str):
        """添加到白名单"""
        self.whitelist.add(identifier)
        logger.info(f"添加到白名单: {identifier}")
    
    def add_to_blacklist(self, identifier: str):
        """添加到黑名单"""
        self.blacklist.add(identifier)
        logger.info(f"添加到黑名单: {identifier}")
    
    def remove_from_whitelist(self, identifier: str):
        """从白名单移除"""
        self.whitelist.discard(identifier)
    
    def remove_from_blacklist(self, identifier: str):
        """从黑名单移除"""
        self.blacklist.discard(identifier)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取限流统计"""
        now = time.time()
        active_clients = 0
        total_requests = 0
        
        for history_key, request_times in self.request_history.items():
            # 清理过期记录
            while request_times and request_times[0] <= now - 3600:  # 1小时
                request_times.popleft()
            
            if request_times:
                active_clients += 1
                total_requests += len(request_times)
        
        return {
            "active_clients": active_clients,
            "total_requests_last_hour": total_requests,
            "blocked_ips": len(self.blocked_ips),
            "whitelist_size": len(self.whitelist),
            "blacklist_size": len(self.blacklist),
            "rate_limits": self.rate_limits
        }


class RateLimitMiddleware:
    """限流中间件"""
    
    def __init__(self, app, limiter: AdvancedRateLimiter):
        self.app = app
        self.limiter = limiter
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # 创建请求对象
        from fastapi import Request
        request = Request(scope, receive)
        
        # 检查限流
        is_limited, limit_info = self.limiter.is_rate_limited(request)
        
        if is_limited:
            # 返回限流响应
            response = {
                "error": "Rate limit exceeded",
                "message": f"Too many requests. Reason: {limit_info.get('reason', 'unknown')}",
                "retry_after": limit_info.get("retry_after", 60),
                "limit": limit_info.get("limit"),
                "window": limit_info.get("window")
            }
            
            # 记录限流事件
            client_id = self.limiter.get_client_identifier(request)
            logger.warning(f"限流触发: {client_id} - {request.method} {request.url.path}")
            
            # 发送429响应
            response_body = {
                "status_code": 429,
                "headers": [
                    (b"content-type", b"application/json"),
                    (b"retry-after", str(limit_info.get("retry_after", 60)).encode()),
                ],
                "body": str(response).encode()
            }
            
            await send({
                "type": "http.response.start",
                "status": 429,
                "headers": response_body["headers"]
            })
            await send({
                "type": "http.response.body",
                "body": response_body["body"]
            })
            return
        
        # 继续处理请求
        await self.app(scope, receive, send)


# 创建全局限流器实例
advanced_limiter = AdvancedRateLimiter()

# 创建slowapi限流器（用于装饰器）
limiter = Limiter(key_func=get_remote_address)


def create_rate_limit_decorator(rate: str, key_func: Optional[Callable] = None):
    """创建限流装饰器"""
    def decorator(func):
        return limiter.limit(rate, key_func=key_func or get_remote_address)(func)
    return decorator


# 常用限流装饰器
auth_limit = create_rate_limit_decorator("5/minute")
ai_limit = create_rate_limit_decorator("30/minute")
upload_limit = create_rate_limit_decorator("10/minute")
general_limit = create_rate_limit_decorator("100/minute")
