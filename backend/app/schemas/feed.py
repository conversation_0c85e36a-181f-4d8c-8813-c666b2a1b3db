"""
动态分享相关的Pydantic模式
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class FeedBase(BaseModel):
    """动态基础模式"""
    content: str = Field(..., min_length=1, max_length=2000, description="动态内容")
    images: Optional[List[str]] = Field(None, description="图片URL列表")
    mood: Optional[str] = Field(None, max_length=50, description="心情标签")
    tags: Optional[List[str]] = Field(None, description="话题标签列表")
    location: Optional[str] = Field(None, max_length=200, description="位置信息")
    is_public: bool = Field(True, description="是否公开")

    @validator('images')
    def validate_images(cls, v):
        if v is not None and len(v) > 9:
            raise ValueError('最多只能上传9张图片')
        return v

    @validator('tags')
    def validate_tags(cls, v):
        if v is not None and len(v) > 10:
            raise ValueError('最多只能添加10个标签')
        return v


class FeedCreate(FeedBase):
    """创建动态的请求模式"""
    pet_id: int = Field(..., description="宠物ID")


class FeedUpdate(BaseModel):
    """更新动态的请求模式"""
    content: Optional[str] = Field(None, min_length=1, max_length=2000, description="动态内容")
    images: Optional[List[str]] = Field(None, description="图片URL列表")
    mood: Optional[str] = Field(None, max_length=50, description="心情标签")
    tags: Optional[List[str]] = Field(None, description="话题标签列表")
    location: Optional[str] = Field(None, max_length=200, description="位置信息")
    is_public: Optional[bool] = Field(None, description="是否公开")

    @validator('images')
    def validate_images(cls, v):
        if v is not None and len(v) > 9:
            raise ValueError('最多只能上传9张图片')
        return v

    @validator('tags')
    def validate_tags(cls, v):
        if v is not None and len(v) > 10:
            raise ValueError('最多只能添加10个标签')
        return v


class FeedResponse(FeedBase):
    """动态响应模式"""
    id: int = Field(..., description="动态ID")
    pet_id: int = Field(..., description="宠物ID")
    user_id: int = Field(..., description="用户ID")
    
    # 统计数据
    likes_count: int = Field(0, description="点赞数")
    comments_count: int = Field(0, description="评论数")
    shares_count: int = Field(0, description="分享数")
    views_count: int = Field(0, description="浏览数")
    
    # AI相关
    ai_generated_content: Optional[str] = Field(None, description="AI生成的文案")
    ai_mood_score: Optional[float] = Field(None, description="AI情感分析得分")
    ai_quality_score: Optional[float] = Field(None, description="AI内容质量评分")
    ai_tags: Optional[List[str]] = Field(None, description="AI自动识别的标签")
    
    # 状态
    status: str = Field("published", description="动态状态")
    is_featured: bool = Field(False, description="是否精选")
    is_ai_generated: bool = Field(False, description="是否AI生成")
    
    # 时间
    published_at: Optional[datetime] = Field(None, description="发布时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    # 用户交互状态
    is_liked: bool = Field(False, description="当前用户是否已点赞")
    
    # 关联信息
    pet_name: Optional[str] = Field(None, description="宠物名称")
    pet_avatar: Optional[str] = Field(None, description="宠物头像")
    user_name: Optional[str] = Field(None, description="用户名")

    class Config:
        from_attributes = True


class FeedListResponse(BaseModel):
    """动态列表响应模式"""
    feeds: List[FeedResponse] = Field(..., description="动态列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    has_more: bool = Field(..., description="是否有更多")


class FeedLikeResponse(BaseModel):
    """点赞响应模式"""
    feed_id: int = Field(..., description="动态ID")
    is_liked: bool = Field(..., description="是否已点赞")
    likes_count: int = Field(..., description="点赞总数")


class FeedCommentBase(BaseModel):
    """评论基础模式"""
    content: str = Field(..., min_length=1, max_length=500, description="评论内容")


class FeedCommentCreate(FeedCommentBase):
    """创建评论的请求模式"""
    parent_id: Optional[int] = Field(None, description="父评论ID（回复评论时使用）")


class FeedCommentResponse(FeedCommentBase):
    """评论响应模式"""
    id: int = Field(..., description="评论ID")
    feed_id: int = Field(..., description="动态ID")
    user_id: int = Field(..., description="用户ID")
    parent_id: Optional[int] = Field(None, description="父评论ID")
    
    # 状态
    is_deleted: bool = Field(False, description="是否已删除")
    is_hidden: bool = Field(False, description="是否隐藏")
    
    # 时间
    created_at: datetime = Field(..., description="评论时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    # 关联信息
    user_name: Optional[str] = Field(None, description="用户名")
    user_avatar: Optional[str] = Field(None, description="用户头像")
    
    # 回复信息
    is_reply: bool = Field(False, description="是否为回复评论")
    reply_count: int = Field(0, description="回复数量")
    replies: Optional[List['FeedCommentResponse']] = Field(None, description="回复列表")

    class Config:
        from_attributes = True


class FeedCommentListResponse(BaseModel):
    """评论列表响应模式"""
    comments: List[FeedCommentResponse] = Field(..., description="评论列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    has_more: bool = Field(..., description="是否有更多")


class FeedQueryParams(BaseModel):
    """动态查询参数"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")
    pet_id: Optional[int] = Field(None, description="宠物ID筛选")
    user_id: Optional[int] = Field(None, description="用户ID筛选")
    mood: Optional[str] = Field(None, description="心情筛选")
    tags: Optional[List[str]] = Field(None, description="标签筛选")
    is_featured: Optional[bool] = Field(None, description="是否精选筛选")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", pattern="^(asc|desc)$", description="排序方向")


class AIContentGenerateRequest(BaseModel):
    """AI内容生成请求"""
    pet_id: int = Field(..., description="宠物ID")
    images: Optional[List[str]] = Field(None, description="图片URL列表")
    mood: Optional[str] = Field(None, description="心情提示")
    context: Optional[str] = Field(None, description="上下文信息")


class AIContentGenerateResponse(BaseModel):
    """AI内容生成响应"""
    content: str = Field(..., description="生成的内容")
    mood: str = Field(..., description="识别的心情")
    tags: List[str] = Field(..., description="推荐的标签")
    quality_score: float = Field(..., description="内容质量评分")


# 更新FeedCommentResponse的前向引用
FeedCommentResponse.model_rebuild()
