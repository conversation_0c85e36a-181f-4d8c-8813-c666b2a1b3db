"""
数据库查询优化工具
包含索引分析、查询优化、性能监控等功能
"""

import time
import asyncio
from typing import Dict, List, Any, Optional
from sqlalchemy import text, inspect
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.core.database import engine, get_db


class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self):
        self.slow_query_threshold = 1.0  # 慢查询阈值（秒）
        self.query_stats = {}
    
    async def analyze_table_indexes(self, db: AsyncSession) -> Dict[str, Any]:
        """分析表索引使用情况"""
        try:
            # 检查各表的索引使用情况
            tables_analysis = {}
            
            # 用户表索引分析
            users_analysis = await self._analyze_table_performance(
                db, "users", 
                ["username", "email", "created_at", "is_active"]
            )
            tables_analysis["users"] = users_analysis
            
            # 宠物表索引分析
            pets_analysis = await self._analyze_table_performance(
                db, "pets",
                ["owner_id", "created_at", "breed", "is_active"]
            )
            tables_analysis["pets"] = pets_analysis
            
            # 动态表索引分析
            feeds_analysis = await self._analyze_table_performance(
                db, "feeds",
                ["user_id", "pet_id", "status", "is_public", "created_at", "is_featured"]
            )
            tables_analysis["feeds"] = feeds_analysis
            
            # 消息表索引分析
            messages_analysis = await self._analyze_table_performance(
                db, "messages",
                ["conversation_id", "user_id", "pet_id", "created_at"]
            )
            tables_analysis["messages"] = messages_analysis
            
            return tables_analysis
            
        except Exception as e:
            logger.error(f"索引分析失败: {e}")
            return {}
    
    async def _analyze_table_performance(
        self, 
        db: AsyncSession, 
        table_name: str, 
        key_columns: List[str]
    ) -> Dict[str, Any]:
        """分析单表性能"""
        try:
            # 获取表基本信息
            result = await db.execute(text(f"""
                SELECT 
                    COUNT(*) as row_count,
                    AVG(LENGTH(CONCAT_WS('', *))) as avg_row_size
                FROM {table_name}
            """))
            table_info = result.fetchone()
            
            # 检查索引使用情况
            index_result = await db.execute(text(f"""
                SHOW INDEX FROM {table_name}
            """))
            indexes = index_result.fetchall()
            
            # 分析查询性能
            performance_stats = {}
            for column in key_columns:
                try:
                    start_time = time.time()
                    await db.execute(text(f"""
                        SELECT COUNT(*) FROM {table_name} 
                        WHERE {column} IS NOT NULL 
                        LIMIT 1000
                    """))
                    query_time = time.time() - start_time
                    performance_stats[column] = query_time
                except Exception as e:
                    performance_stats[column] = f"Error: {str(e)}"
            
            return {
                "row_count": table_info[0] if table_info else 0,
                "avg_row_size": table_info[1] if table_info else 0,
                "indexes": [dict(idx) for idx in indexes],
                "query_performance": performance_stats
            }
            
        except Exception as e:
            logger.error(f"表{table_name}性能分析失败: {e}")
            return {"error": str(e)}
    
    async def create_missing_indexes(self, db: AsyncSession) -> List[str]:
        """创建缺失的索引"""
        created_indexes = []
        
        try:
            # 检查并创建必要的复合索引
            index_definitions = [
                # 用户表索引
                ("idx_users_active_created", "users", ["is_active", "created_at"]),
                ("idx_users_provider", "users", ["provider", "provider_id"]),
                
                # 宠物表索引
                ("idx_pets_owner_active", "pets", ["owner_id", "is_active"]),
                ("idx_pets_breed_size", "pets", ["breed", "size"]),
                ("idx_pets_mood_updated", "pets", ["current_mood", "updated_at"]),
                
                # 动态表索引（已在模型中定义，这里检查是否存在）
                ("idx_feeds_public_featured", "feeds", ["is_public", "is_featured", "created_at"]),
                ("idx_feeds_status_created", "feeds", ["status", "created_at"]),
                ("idx_feeds_user_pet", "feeds", ["user_id", "pet_id"]),
                
                # 点赞表索引（已在模型中定义）
                ("idx_feed_likes_created", "feed_likes", ["created_at"]),
                
                # 评论表索引（已在模型中定义）
                ("idx_feed_comments_status", "feed_comments", ["is_deleted", "is_hidden"]),
                
                # 消息表索引
                ("idx_messages_type_created", "messages", ["message_type", "created_at"]),
                ("idx_messages_user_pet", "messages", ["user_id", "pet_id"]),
                
                # 对话表索引
                ("idx_conversations_user_active", "conversations", ["user_id", "is_active"]),
                ("idx_conversations_pet_updated", "conversations", ["pet_id", "updated_at"]),
            ]
            
            for index_name, table_name, columns in index_definitions:
                try:
                    # 检查索引是否已存在
                    check_result = await db.execute(text(f"""
                        SELECT COUNT(*) FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() 
                        AND table_name = '{table_name}' 
                        AND index_name = '{index_name}'
                    """))
                    
                    if check_result.scalar() == 0:
                        # 创建索引
                        columns_str = ", ".join(columns)
                        await db.execute(text(f"""
                            CREATE INDEX {index_name} ON {table_name} ({columns_str})
                        """))
                        await db.commit()
                        created_indexes.append(f"{index_name} on {table_name}")
                        logger.info(f"创建索引: {index_name} on {table_name}")
                    
                except Exception as e:
                    logger.warning(f"创建索引{index_name}失败: {e}")
                    continue
            
            return created_indexes
            
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            return []
    
    async def optimize_connection_pool(self) -> Dict[str, Any]:
        """优化数据库连接池配置"""
        try:
            # 获取当前连接池状态
            pool_status = {
                "pool_size": engine.pool.size(),
                "checked_in": engine.pool.checkedin(),
                "checked_out": engine.pool.checkedout(),
                "overflow": engine.pool.overflow(),
                "invalid": engine.pool.invalid()
            }
            
            # 连接池优化建议
            recommendations = []
            
            if pool_status["checked_out"] / engine.pool.size() > 0.8:
                recommendations.append("考虑增加连接池大小")
            
            if pool_status["overflow"] > 10:
                recommendations.append("考虑增加max_overflow设置")
            
            if pool_status["invalid"] > 0:
                recommendations.append("检查数据库连接稳定性")
            
            return {
                "current_status": pool_status,
                "recommendations": recommendations
            }
            
        except Exception as e:
            logger.error(f"连接池分析失败: {e}")
            return {"error": str(e)}
    
    async def analyze_slow_queries(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """分析慢查询"""
        try:
            # 启用慢查询日志分析
            slow_queries = []
            
            # 检查MySQL慢查询设置
            result = await db.execute(text("SHOW VARIABLES LIKE 'slow_query_log'"))
            slow_log_status = result.fetchone()
            
            result = await db.execute(text("SHOW VARIABLES LIKE 'long_query_time'"))
            long_query_time = result.fetchone()
            
            # 获取当前进程列表中的长时间运行查询
            result = await db.execute(text("""
                SELECT 
                    ID, USER, HOST, DB, COMMAND, TIME, STATE, INFO
                FROM information_schema.PROCESSLIST 
                WHERE COMMAND != 'Sleep' 
                AND TIME > 1
                ORDER BY TIME DESC
                LIMIT 10
            """))
            
            long_running_queries = result.fetchall()
            
            return {
                "slow_log_enabled": slow_log_status[1] if slow_log_status else "Unknown",
                "long_query_time": long_query_time[1] if long_query_time else "Unknown",
                "long_running_queries": [dict(query) for query in long_running_queries]
            }
            
        except Exception as e:
            logger.error(f"慢查询分析失败: {e}")
            return []
    
    async def get_table_statistics(self, db: AsyncSession) -> Dict[str, Any]:
        """获取表统计信息"""
        try:
            result = await db.execute(text("""
                SELECT 
                    table_name,
                    table_rows,
                    data_length,
                    index_length,
                    (data_length + index_length) as total_size
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY total_size DESC
            """))
            
            tables = result.fetchall()
            
            return {
                "tables": [dict(table) for table in tables],
                "total_tables": len(tables)
            }
            
        except Exception as e:
            logger.error(f"表统计信息获取失败: {e}")
            return {}


# 全局优化器实例
db_optimizer = DatabaseOptimizer()


async def run_database_optimization():
    """运行数据库优化"""
    logger.info("开始数据库优化...")
    
    async for db in get_db():
        try:
            # 1. 分析表索引
            logger.info("分析表索引...")
            index_analysis = await db_optimizer.analyze_table_indexes(db)
            
            # 2. 创建缺失的索引
            logger.info("创建缺失的索引...")
            created_indexes = await db_optimizer.create_missing_indexes(db)
            
            # 3. 分析连接池
            logger.info("分析连接池状态...")
            pool_analysis = await db_optimizer.optimize_connection_pool()
            
            # 4. 分析慢查询
            logger.info("分析慢查询...")
            slow_query_analysis = await db_optimizer.analyze_slow_queries(db)
            
            # 5. 获取表统计信息
            logger.info("获取表统计信息...")
            table_stats = await db_optimizer.get_table_statistics(db)
            
            # 输出优化报告
            optimization_report = {
                "timestamp": time.time(),
                "index_analysis": index_analysis,
                "created_indexes": created_indexes,
                "pool_analysis": pool_analysis,
                "slow_query_analysis": slow_query_analysis,
                "table_statistics": table_stats
            }
            
            logger.info(f"数据库优化完成，创建了{len(created_indexes)}个索引")
            return optimization_report
            
        except Exception as e:
            logger.error(f"数据库优化失败: {e}")
            return {"error": str(e)}
        finally:
            await db.close()


if __name__ == "__main__":
    asyncio.run(run_database_optimization())
