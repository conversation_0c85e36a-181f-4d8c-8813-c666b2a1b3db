"""
增强的日志系统
提供结构化日志、错误追踪、性能监控等功能
"""

import sys
import os
import json
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger
from app.core.config import settings


class EnhancedLogger:
    """增强的日志器"""
    
    def __init__(self):
        self.setup_enhanced_logging()
        self.error_tracker = ErrorTracker()
    
    def setup_enhanced_logging(self):
        """配置增强的日志系统"""
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # JSON格式化器
        def json_formatter(record):
            """JSON格式化器"""
            log_entry = {
                "timestamp": record["time"].isoformat(),
                "level": record["level"].name,
                "logger": record["name"],
                "function": record["function"],
                "line": record["line"],
                "message": record["message"],
                "module": record["module"],
                "process": record["process"].id,
                "thread": record["thread"].id,
            }
            
            # 添加额外字段
            if record["extra"]:
                log_entry["extra"] = record["extra"]
            
            # 添加异常信息
            if record["exception"]:
                log_entry["exception"] = {
                    "type": record["exception"].type.__name__,
                    "value": str(record["exception"].value),
                    "traceback": record["exception"].traceback
                }
            
            return json.dumps(log_entry, ensure_ascii=False)
        
        # 应用日志文件（JSON格式）
        logger.add(
            log_dir / "app.jsonl",
            format=json_formatter,
            level="DEBUG",
            rotation="50 MB",
            retention="30 days",
            compression="gz",
            backtrace=True,
            diagnose=True,
            serialize=False
        )
        
        # 错误日志文件
        logger.add(
            log_dir / "error.log",
            format=self._error_format,
            level="ERROR",
            rotation="20 MB",
            retention="90 days",
            compression="gz",
            backtrace=True,
            diagnose=True
        )
        
        # 访问日志文件
        logger.add(
            log_dir / "access.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {extra[method]} {extra[path]} | {extra[status_code]} | {extra[response_time]}ms | {extra[client_ip]}",
            level="INFO",
            rotation="100 MB",
            retention="30 days",
            compression="gz",
            filter=lambda record: "access_log" in record["extra"]
        )
        
        # 性能日志文件
        logger.add(
            log_dir / "performance.log",
            format=json_formatter,
            level="INFO",
            rotation="50 MB",
            retention="7 days",
            compression="gz",
            filter=lambda record: "performance" in record["extra"]
        )
        
        # 安全日志文件
        logger.add(
            log_dir / "security.log",
            format=json_formatter,
            level="WARNING",
            rotation="20 MB",
            retention="180 days",
            compression="gz",
            filter=lambda record: "security" in record["extra"]
        )
        
        logger.info("增强日志系统初始化完成")
    
    def _error_format(self, record):
        """错误日志格式"""
        error_info = {
            "timestamp": record["time"].isoformat(),
            "level": record["level"].name,
            "message": record["message"],
            "location": f"{record['name']}:{record['function']}:{record['line']}",
            "extra": record["extra"]
        }
        
        if record["exception"]:
            error_info["exception"] = {
                "type": record["exception"].type.__name__,
                "value": str(record["exception"].value),
                "traceback": traceback.format_exception(
                    record["exception"].type,
                    record["exception"].value,
                    record["exception"].traceback
                )
            }
        
        return json.dumps(error_info, ensure_ascii=False, indent=2)
    
    def log_request(self, method: str, path: str, status_code: int, 
                   response_time: float, client_ip: str, user_id: Optional[int] = None):
        """记录访问日志"""
        logger.bind(
            access_log=True,
            method=method,
            path=path,
            status_code=status_code,
            response_time=response_time,
            client_ip=client_ip,
            user_id=user_id
        ).info("API访问")
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能日志"""
        logger.bind(
            performance=True,
            operation=operation,
            duration=duration,
            **kwargs
        ).info(f"性能指标: {operation}")
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          severity: str = "warning"):
        """记录安全事件"""
        log_func = getattr(logger.bind(security=True, event_type=event_type, **details), severity)
        log_func(f"安全事件: {event_type}")
    
    def log_business_event(self, event: str, **kwargs):
        """记录业务事件"""
        logger.bind(business=True, event=event, **kwargs).info(f"业务事件: {event}")


class ErrorTracker:
    """错误追踪器"""
    
    def __init__(self):
        self.error_counts = {}
        self.error_details = {}
    
    def track_error(self, error: Exception, context: Dict[str, Any] = None):
        """追踪错误"""
        error_key = f"{type(error).__name__}:{str(error)}"
        
        # 计数
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # 记录详情
        if error_key not in self.error_details:
            self.error_details[error_key] = {
                "first_seen": datetime.now().isoformat(),
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context or {},
                "traceback": traceback.format_exc()
            }
        
        self.error_details[error_key]["last_seen"] = datetime.now().isoformat()
        self.error_details[error_key]["count"] = self.error_counts[error_key]
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        return {
            "total_errors": sum(self.error_counts.values()),
            "unique_errors": len(self.error_counts),
            "top_errors": sorted(
                self.error_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10],
            "error_details": self.error_details
        }


class LogAnalyzer:
    """日志分析器"""
    
    @staticmethod
    def analyze_log_file(file_path: str, lines: int = 1000) -> Dict[str, Any]:
        """分析日志文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                log_lines = f.readlines()[-lines:]
            
            analysis = {
                "total_lines": len(log_lines),
                "level_counts": {},
                "recent_errors": [],
                "patterns": {}
            }
            
            for line in log_lines:
                try:
                    if line.strip().startswith('{'):
                        # JSON格式日志
                        log_entry = json.loads(line.strip())
                        level = log_entry.get("level", "UNKNOWN")
                        analysis["level_counts"][level] = analysis["level_counts"].get(level, 0) + 1
                        
                        if level in ["ERROR", "CRITICAL"] and len(analysis["recent_errors"]) < 10:
                            analysis["recent_errors"].append({
                                "timestamp": log_entry.get("timestamp"),
                                "message": log_entry.get("message"),
                                "location": f"{log_entry.get('logger')}:{log_entry.get('function')}"
                            })
                    else:
                        # 普通格式日志
                        if " ERROR " in line:
                            analysis["level_counts"]["ERROR"] = analysis["level_counts"].get("ERROR", 0) + 1
                        elif " WARNING " in line:
                            analysis["level_counts"]["WARNING"] = analysis["level_counts"].get("WARNING", 0) + 1
                        elif " INFO " in line:
                            analysis["level_counts"]["INFO"] = analysis["level_counts"].get("INFO", 0) + 1
                
                except json.JSONDecodeError:
                    continue
            
            return analysis
            
        except Exception as e:
            return {"error": f"日志分析失败: {str(e)}"}


# 全局实例
enhanced_logger = EnhancedLogger()
error_tracker = enhanced_logger.error_tracker
log_analyzer = LogAnalyzer()


# 便捷函数
def log_request(method: str, path: str, status_code: int, response_time: float, 
               client_ip: str, user_id: Optional[int] = None):
    """记录请求日志"""
    enhanced_logger.log_request(method, path, status_code, response_time, client_ip, user_id)


def log_performance(operation: str, duration: float, **kwargs):
    """记录性能日志"""
    enhanced_logger.log_performance(operation, duration, **kwargs)


def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "warning"):
    """记录安全事件"""
    enhanced_logger.log_security_event(event_type, details, severity)


def log_business_event(event: str, **kwargs):
    """记录业务事件"""
    enhanced_logger.log_business_event(event, **kwargs)
