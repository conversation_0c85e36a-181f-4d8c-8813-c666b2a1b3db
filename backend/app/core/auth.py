"""
认证相关工具函数
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.user import User

# 创建HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    获取当前用户（可选）
    如果没有提供认证信息或认证失败，返回None
    """
    if not credentials:
        return None
    
    try:
        # 这里应该验证JWT token，暂时简化处理
        # 在实际项目中，需要解析JWT token获取用户ID
        token = credentials.credentials
        
        # 简化处理：假设token就是用户ID
        # 实际应该解析JWT token
        if token.isdigit():
            user_id = int(token)
            user = db.query(User).filter(User.id == user_id).first()
            return user
        
        return None
        
    except Exception:
        return None


async def get_current_user_required(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    获取当前用户（必需）
    如果没有提供认证信息或认证失败，抛出异常
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要认证",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 这里应该验证JWT token，暂时简化处理
        token = credentials.credentials
        
        # 简化处理：假设token就是用户ID
        if token.isdigit():
            user_id = int(token)
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                return user
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证信息",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


def verify_user_permission(current_user: User, target_user_id: int) -> bool:
    """验证用户权限"""
    return current_user.id == target_user_id


def verify_pet_owner(current_user: User, pet_owner_id: int) -> bool:
    """验证宠物所有者权限"""
    return current_user.id == pet_owner_id
