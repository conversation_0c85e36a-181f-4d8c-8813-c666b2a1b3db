#!/usr/bin/env python3
"""
iOS应用测试用的简化API服务器
端口: 8000
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
from datetime import datetime

app = FastAPI(title="毛孩子AI测试API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class User(BaseModel):
    id: int
    username: str
    email: str
    provider: str = "local"
    displayName: Optional[str] = None
    avatar: Optional[str] = None
    petCount: int = 0
    feedCount: int = 0
    chatCount: int = 0
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None

class LoginRequest(BaseModel):
    username: str
    password: str

class RegisterRequest(BaseModel):
    username: str
    password: str
    email: Optional[str] = None

class AuthResponse(BaseModel):
    success: bool
    message: str
    user: Optional[User] = None
    token: Optional[str] = None

class Pet(BaseModel):
    id: int
    name: str
    breed: str
    age: Optional[int] = None
    gender: Optional[str] = None
    color: Optional[str] = None
    personality: Optional[str] = None
    personalityTags: Optional[List[str]] = None
    currentMood: str = "开心"
    responseStyle: str = "友好"
    userId: int
    createdAt: str
    updatedAt: str

class Feed(BaseModel):
    id: int
    content: str
    petId: int
    petName: str
    userId: int
    username: str
    images: Optional[List[str]] = None
    location: Optional[str] = None
    mood: Optional[str] = None
    likesCount: int = 0
    commentsCount: int = 0
    isLiked: bool = False
    createdAt: str
    updatedAt: str

class ChatRequest(BaseModel):
    message: str
    petId: int
    contextData: Optional[dict] = None

class ChatResponse(BaseModel):
    reply: str
    mood: str
    actions: List[str] = []
    emotions: List[str] = []
    confidence: float = 0.9
    conversationId: int = 1
    messageId: int
    responseTime: float = 1.2
    tokensUsed: int = 50

# 模拟数据
mock_users = {
    "admin": User(
        id=1,
        username="admin",
        email="<EMAIL>",
        displayName="管理员",
        petCount=2,
        feedCount=5,
        chatCount=20,
        createdAt="2024-01-01T00:00:00Z",
        updatedAt="2024-01-01T00:00:00Z"
    )
}

mock_pets = [
    Pet(
        id=1,
        name="小白",
        breed="金毛",
        age=3,
        gender="雄性",
        color="金色",
        personality="活泼开朗",
        personalityTags=["友好", "活泼", "聪明"],
        currentMood="开心",
        responseStyle="友好",
        userId=1,
        createdAt="2024-01-01T00:00:00Z",
        updatedAt="2024-01-01T00:00:00Z"
    ),
    Pet(
        id=2,
        name="小黑",
        breed="拉布拉多",
        age=2,
        gender="雌性",
        color="黑色",
        personality="温顺可爱",
        personalityTags=["温顺", "可爱", "忠诚"],
        currentMood="平静",
        responseStyle="温和",
        userId=1,
        createdAt="2024-01-01T00:00:00Z",
        updatedAt="2024-01-01T00:00:00Z"
    )
]

mock_feeds = [
    Feed(
        id=1,
        content="今天带小白去公园玩，它特别开心！🐕",
        petId=1,
        petName="小白",
        userId=1,
        username="admin",
        images=["https://example.com/image1.jpg"],
        location="中央公园",
        mood="开心",
        likesCount=15,
        commentsCount=3,
        isLiked=False,
        createdAt="2024-06-26T10:00:00Z",
        updatedAt="2024-06-26T10:00:00Z"
    ),
    Feed(
        id=2,
        content="小黑今天学会了新技能，坐下！👏",
        petId=2,
        petName="小黑",
        userId=1,
        username="admin",
        mood="骄傲",
        likesCount=8,
        commentsCount=2,
        isLiked=True,
        createdAt="2024-06-26T09:00:00Z",
        updatedAt="2024-06-26T09:00:00Z"
    )
]

# API路由
@app.get("/")
async def root():
    return {"message": "🐾 毛孩子AI测试API服务运行中", "status": "ok", "version": "1.0.0"}

@app.get("/api/v1/auth/health")
async def health():
    return {"status": "healthy", "service": "furrykids-ai-test"}

# 认证相关API
@app.post("/api/v1/auth/login", response_model=AuthResponse)
async def login(request: LoginRequest):
    if request.username in mock_users:
        user = mock_users[request.username]
        return AuthResponse(
            success=True,
            message="登录成功",
            user=user,
            token="mock_jwt_token_12345"
        )
    else:
        return AuthResponse(
            success=False,
            message="用户名或密码错误"
        )

@app.post("/api/v1/auth/register", response_model=AuthResponse)
async def register(request: RegisterRequest):
    if request.username in mock_users:
        return AuthResponse(
            success=False,
            message="用户名已存在"
        )
    
    new_user = User(
        id=len(mock_users) + 1,
        username=request.username,
        email=request.email or f"{request.username}@example.com",
        displayName=request.username,
        createdAt=datetime.now().isoformat() + "Z",
        updatedAt=datetime.now().isoformat() + "Z"
    )
    
    mock_users[request.username] = new_user
    
    return AuthResponse(
        success=True,
        message="注册成功",
        user=new_user,
        token="mock_jwt_token_12345"
    )

# 宠物相关API
@app.get("/api/v1/pets")
async def get_pets():
    return {
        "pets": mock_pets,
        "total": len(mock_pets),
        "page": 1,
        "size": 10,
        "hasMore": False
    }

# 动态相关API
@app.get("/api/v1/feeds")
async def get_feeds():
    return {
        "feeds": mock_feeds,
        "total": len(mock_feeds),
        "page": 1,
        "size": 10,
        "hasMore": False
    }

@app.post("/api/v1/feeds/{feed_id}/like")
async def like_feed(feed_id: int):
    return {"message": f"点赞动态 {feed_id} 成功", "success": True}

# AI聊天相关API
@app.post("/api/v1/ai/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    # 简单的回复逻辑
    replies = [
        "汪汪！主人你好呀！🐕",
        "我今天心情很好呢～想和你一起玩！",
        "你说的话我都听懂了，我很聪明的！",
        "我爱你，主人！❤️",
        "今天天气真好，我们去散步吧！",
        "我饿了，有好吃的吗？🍖"
    ]
    
    import random
    reply = random.choice(replies)
    
    return ChatResponse(
        reply=reply,
        mood="开心",
        actions=["摇尾巴", "跳跃"],
        emotions=["快乐", "兴奋"],
        messageId=random.randint(1000, 9999)
    )

if __name__ == "__main__":
    print("🚀 启动毛孩子AI测试API服务...")
    print("📍 地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔧 测试模式: 使用模拟数据")
    print("-" * 50)
    
    uvicorn.run(
        "test_api_server:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
