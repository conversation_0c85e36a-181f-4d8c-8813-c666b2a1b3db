import Foundation
import Combine
import AVFoundation

// MARK: - 互动动作枚举
enum InteractionAction: String, CaseIterable {
    case feed = "喂食"
    case pet = "抚摸"
    case play = "玩耍"
    case walk = "散步"
    case wash = "洗澡"
    case train = "训练"

    var icon: String {
        switch self {
        case .feed: return "🍖"
        case .pet: return "🤗"
        case .play: return "🎾"
        case .walk: return "🚶‍♂️"
        case .wash: return "🛁"
        case .train: return "🎓"
        }
    }
}

class InteractionStore: NSObject, ObservableObject {
    @Published var messages: [Message] = []
    @Published var isLoading = false
    @Published var error: String?
    @Published var isTyping = false
    @Published var isSpeaking = false
    @Published var currentConversationId: Int?

    private let aiService = AIService.shared
    private var speechSynthesizer = AVSpeechSynthesizer()
    private var cancellables = Set<AnyCancellable>()

    override init() {
        super.init()
        setupAudioSession()
        setupSpeechSynthesizer()

        // 添加欢迎消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.addWelcomeMessage()
        }
    }
    
    // MARK: - Audio Session Setup
    
    private func setupAudioSession() {
        #if !targetEnvironment(simulator)
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("音频会话设置失败: \(error)")
        }
        #else
        // 模拟器环境下跳过音频会话配置以避免警告
        print("模拟器环境：跳过音频会话配置")
        #endif
    }
    
    // MARK: - Message Actions
    
    // 发送用户消息
    func sendMessage(_ content: String, petId: Int = 1) {
        // 添加用户消息到界面
        let userMessage = Message(
            id: Int.random(in: 1000...9999),
            conversationId: currentConversationId ?? 1,
            content: content,
            type: .user,
            mood: nil,
            actions: nil,
            emotions: nil,
            confidence: nil,
            tokensUsed: nil,
            createdAt: Date(),
            isRead: true
        )
        messages.append(userMessage)

        // 显示AI正在输入状态
        isTyping = true
        isLoading = true
        error = nil

        // 构建AI请求
        let request = AIRequest(
            message: content,
            petId: String(petId),
            petName: "小毛球", // 临时使用固定名称
            petPersonality: "活泼、好奇、友善", // 临时使用固定性格
            conversationHistory: messages,
            timestamp: Date()
        )

        // 调用真实的AI服务
        aiService.sendMessage(request)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isTyping = false
                        self?.isLoading = false

                        if case .failure(let error) = completion {
                            self?.error = self?.handleAPIError(error)
                            // 如果AI服务失败，使用备用回复
                            self?.addFallbackMessage()
                        }
                    }
                },
                receiveValue: { [weak self] response in
                    DispatchQueue.main.async {
                        // 添加AI回复消息
                        let aiMessage = Message(
                            id: Int.random(in: 1000...9999),
                            conversationId: 1,
                            content: response.reply,
                            type: .pet,
                            mood: response.mood,
                            actions: response.actions,
                            emotions: nil,
                            confidence: response.confidence,
                            tokensUsed: nil,
                            createdAt: Date(),
                            isRead: true
                        )
                        self?.messages.append(aiMessage)
                        self?.currentConversationId = 1

                        // 自动语音播报
                        self?.speakMessage(response.reply)
                    }
                }
            )
            .store(in: &cancellables)
    }

    // 添加欢迎消息
    private func addWelcomeMessage() {
        let welcomeMessage = Message(
            id: Int.random(in: 1000...9999),
            conversationId: currentConversationId ?? 1,
            content: "你好！很高兴见到你！今天想和我聊点什么呢？",
            type: .pet,
            mood: "开心",
            actions: ["摇尾巴", "看向主人"],
            emotions: ["开心", "兴奋"],
            confidence: 0.9,
            tokensUsed: nil,
            createdAt: Date(),
            isRead: true
        )
        messages.append(welcomeMessage)
    }

    // 添加备用消息（当AI服务失败时）
    private func addFallbackMessage() {
        let fallbackMessages = [
            "抱歉，我刚才走神了，能再说一遍吗？",
            "我需要一点时间思考，稍等一下哦~",
            "网络好像有点问题，不过我还是很想和你聊天！",
            "让我整理一下思路，马上回复你！"
        ]

        let randomMessage = fallbackMessages.randomElement() ?? fallbackMessages[0]
        let fallbackMsg = Message(
            id: Int.random(in: 1000...9999),
            conversationId: currentConversationId ?? 1,
            content: randomMessage,
            type: .pet,
            mood: "困惑",
            actions: ["挠头", "歪头"],
            emotions: ["困惑", "抱歉"],
            confidence: 0.5,
            tokensUsed: nil,
            createdAt: Date(),
            isRead: true
        )
        messages.append(fallbackMsg)

        // 语音播报备用消息
        speakMessage(randomMessage)
    }
    
    // MARK: - AI Reply Generation
    private func generateAIReply(for userMessage: String) -> (reply: String, mood: String) {
        let lowercased = userMessage.lowercased()
        
        // 关键词匹配回复
        if lowercased.contains("你好") || lowercased.contains("hi") || lowercased.contains("hello") {
            return ("你好呀！我是你的小伙伴，很高兴和你聊天！", "开心")
        } else if lowercased.contains("吃") || lowercased.contains("饿") {
            return ("哇！我也饿了呢，想吃好吃的小零食！🍖", "兴奋")
        } else if lowercased.contains("玩") || lowercased.contains("游戏") {
            return ("太好了！我们来玩游戏吧！我最喜欢玩球球了！🎾", "兴奋")
        } else if lowercased.contains("累") || lowercased.contains("困") {
            return ("那我们一起休息一下吧，我陪着你～", "温柔")
        } else if lowercased.contains("散步") || lowercased.contains("走") {
            return ("出去散步！我最喜欢出门了，可以看到好多新鲜的东西！", "兴奋")
        } else if lowercased.contains("洗澡") {
            return ("洗澡澡～虽然有点紧张，但是洗完会很香呢！", "紧张")
        } else if lowercased.contains("学习") || lowercased.contains("训练") {
            return ("好的！我会认真学习的，做一个聪明的好宝宝！", "认真")
        } else if lowercased.contains("爱") || lowercased.contains("喜欢") {
            return ("我也很爱你呢！你是世界上最好的主人！❤️", "爱意")
        } else {
            // 默认回复
            let defaultReplies = [
                ("真的吗？听起来很有趣呢！", "好奇"),
                ("嗯嗯，我在认真听你说话呢！", "专注"),
                ("哇，你说的好棒！", "开心"),
                ("我觉得你说得对呢！", "赞同"),
                ("还有吗？我想听更多！", "好奇"),
                ("你真是太聪明了！", "崇拜")
            ]
            return defaultReplies.randomElement() ?? ("我在想该怎么回答你呢～", "思考")
        }
    }
    
    // MARK: - Speech Synthesis
    
    private func setupSpeechSynthesizer() {
        speechSynthesizer.delegate = self
    }
    
    func speakMessage(_ text: String) {
        // 如果正在播放，先停止
        if speechSynthesizer.isSpeaking {
            speechSynthesizer.stopSpeaking(at: .immediate)
        }
        
        // 清理文本
        let cleanText = cleanTextForSpeech(text)
        
        let utterance = AVSpeechUtterance(string: cleanText)
        
        // 设置中文语音
        utterance.voice = getBestChineseVoice()
        utterance.rate = 0.4 // 稍微慢一点，更像宠物的语调
        utterance.pitchMultiplier = 1.1 // 稍微高一点，更可爱
        utterance.volume = 0.8
        
        isSpeaking = true
        speechSynthesizer.speak(utterance)
    }
    
    func stopSpeaking() {
        speechSynthesizer.stopSpeaking(at: .immediate)
        isSpeaking = false
    }
    
    // MARK: - Helper Methods
    private func cleanTextForSpeech(_ text: String) -> String {
        return text
            .replacingOccurrences(of: "🍖", with: "")
            .replacingOccurrences(of: "🎾", with: "")
            .replacingOccurrences(of: "❤️", with: "")
            .replacingOccurrences(of: "～", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    private func getBestChineseVoice() -> AVSpeechSynthesisVoice? {
        let chineseVoices = AVSpeechSynthesisVoice.speechVoices().filter { voice in
            voice.language.hasPrefix("zh")
        }
        
        // 优先选择中文语音
        return chineseVoices.first ?? AVSpeechSynthesisVoice(language: "zh-CN")
    }
    
    // MARK: - 触发互动
    func triggerInteraction(_ action: InteractionAction) {
        // 发送用户行为消息
        let userActionMessages = [
            InteractionAction.feed: "我给你带来了美味的食物！",
            InteractionAction.pet: "摸摸头，做得好！",
            InteractionAction.play: "来玩球吧！",
            InteractionAction.walk: "我们去散步吧！",
            InteractionAction.wash: "该洗澡了，会很舒服的！",
            InteractionAction.train: "来学习一个新技能吧！"
        ]
        
        if let userMessage = userActionMessages[action] {
            sendMessage(userMessage)
        }
    }
    
    func clearMessages() {
        messages.removeAll()
        currentConversationId = nil
        stopSpeaking()
    }

    // MARK: - 错误处理
    private func handleAPIError(_ error: APIError) -> String {
        switch error {
        case .invalidURL:
            return "请求地址无效"
        case .noData:
            return "服务器没有返回数据"
        case .decodingError:
            return "数据解析错误"
        case .networkError(let underlyingError):
            return "网络连接失败：\(underlyingError.localizedDescription)"
        case .serverError(let code, let message):
            return message ?? "服务器错误 (\(code))"
        case .unauthorized:
            return "登录已过期，请重新登录"
        case .timeout:
            return "请求超时，请检查网络连接"
        case .noInternetConnection:
            return "无网络连接，请检查网络设置"
        case .rateLimited:
            return "请求过于频繁，请稍后重试"
        case .maintenance:
            return "服务器正在维护中，请稍后重试"
        case .unknown(let message):
            return "未知错误：\(message)"
        }
    }

    // MARK: - 会话管理
    func startNewConversation(petId: Int) {
        // 清除当前会话
        messages.removeAll()
        currentConversationId = nil
        error = nil

        // 添加新的欢迎消息
        addWelcomeMessage()
    }

    // 加载对话历史 (集成真实API)
    func loadConversationHistory(conversationId: Int) {
        isLoading = true
        error = nil

        aiService.getConversationHistory(conversationId: conversationId)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false

                        if case .failure(let error) = completion {
                            self?.error = self?.handleAPIError(error)
                        }
                    }
                },
                receiveValue: { [weak self] (messages: [Message]) in
                    DispatchQueue.main.async {
                        self?.messages = messages
                        self?.currentConversationId = conversationId
                    }
                }
            )
            .store(in: &cancellables)
    }
}

// MARK: - AVSpeechSynthesizerDelegate
extension InteractionStore: AVSpeechSynthesizerDelegate {
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isSpeaking = false
        }
    }
    
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didCancel utterance: AVSpeechUtterance) {
        DispatchQueue.main.async {
            self.isSpeaking = false
        }
    }
} 