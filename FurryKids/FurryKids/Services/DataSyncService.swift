import Foundation
import Combine

// MARK: - Sync Operation Types
enum SyncOperation: String, Codable {
    case createFeed = "create_feed"
    case updateFeed = "update_feed"
    case deleteFeed = "delete_feed"
    case likeFeed = "like_feed"
    case addComment = "add_comment"
    case updatePet = "update_pet"
    case sendMessage = "send_message"
}

// MARK: - Pending Operation
struct PendingOperation: Codable, Identifiable {
    let id: String
    let operation: SyncOperation
    let data: Data
    let timestamp: Date
    let retryCount: Int
    
    init(operation: SyncOperation, data: Data, retryCount: Int = 0) {
        self.id = UUID().uuidString
        self.operation = operation
        self.data = data
        self.timestamp = Date()
        self.retryCount = retryCount
    }
}

// MARK: - Data Sync Service Protocol
protocol DataSyncServiceProtocol {
    func addPendingOperation(_ operation: PendingOperation)
    func syncPendingOperations() -> AnyPublisher<Bool, Never>
    func syncAllData() -> AnyPublisher<Bool, Never>
    func enableOfflineMode()
    func disableOfflineMode()
}

// MARK: - Data Sync Service Implementation
class DataSyncService: DataSyncServiceProtocol, ObservableObject {
    static let shared = DataSyncService()
    
    private let cacheService = CacheService.shared
    private let networkManager = NetworkManager.shared
    private let feedService = FeedService.shared
    private let petService = PetService.shared
    private let aiService = AIService.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    @Published var isOfflineMode = false
    @Published var isSyncing = false
    @Published var pendingOperationsCount = 0
    @Published var lastSyncDate: Date?
    
    private let pendingOperationsKey = "pending_operations"
    private let maxRetryCount = 3
    
    private init() {
        setupNetworkMonitoring()
        loadPendingOperations()
    }
    
    // MARK: - Network Monitoring
    private func setupNetworkMonitoring() {
        networkManager.$isConnected
            .sink { [weak self] isConnected in
                if isConnected && self?.isOfflineMode == true {
                    self?.disableOfflineMode()
                    self?.syncPendingOperations()
                        .sink { _ in }
                        .store(in: &self?.cancellables ?? Set<AnyCancellable>())
                } else if !isConnected {
                    self?.enableOfflineMode()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Offline Mode Management
    func enableOfflineMode() {
        isOfflineMode = true
        print("📱 Offline mode enabled")
    }
    
    func disableOfflineMode() {
        isOfflineMode = false
        print("🌐 Online mode enabled")
    }
    
    // MARK: - Pending Operations Management
    func addPendingOperation(_ operation: PendingOperation) {
        var operations = getPendingOperations()
        operations.append(operation)
        savePendingOperations(operations)
        pendingOperationsCount = operations.count
        
        print("📝 Added pending operation: \(operation.operation.rawValue)")
    }
    
    private func getPendingOperations() -> [PendingOperation] {
        return cacheService.retrieve([PendingOperation].self, forKey: pendingOperationsKey) ?? []
    }
    
    private func savePendingOperations(_ operations: [PendingOperation]) {
        cacheService.store(operations, forKey: pendingOperationsKey, expiration: nil)
    }
    
    private func loadPendingOperations() {
        let operations = getPendingOperations()
        pendingOperationsCount = operations.count
    }
    
    // MARK: - Data Synchronization
    func syncPendingOperations() -> AnyPublisher<Bool, Never> {
        guard !isSyncing else {
            return Just(false).eraseToAnyPublisher()
        }
        
        isSyncing = true
        let operations = getPendingOperations()
        
        guard !operations.isEmpty else {
            isSyncing = false
            return Just(true).eraseToAnyPublisher()
        }
        
        print("🔄 Syncing \(operations.count) pending operations...")
        
        let publishers = operations.map { operation in
            self.executePendingOperation(operation)
        }
        
        return Publishers.MergeMany(publishers)
            .collect()
            .map { results in
                let successCount = results.filter { $0 }.count
                print("✅ Synced \(successCount)/\(results.count) operations")
                return successCount == results.count
            }
            .handleEvents(
                receiveOutput: { [weak self] _ in
                    self?.isSyncing = false
                    self?.lastSyncDate = Date()
                },
                receiveCompletion: { [weak self] _ in
                    self?.isSyncing = false
                }
            )
            .replaceError(with: false)
            .eraseToAnyPublisher()
    }
    
    private func executePendingOperation(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        switch operation.operation {
        case .createFeed:
            return executeFeedCreation(operation)
        case .updateFeed:
            return executeFeedUpdate(operation)
        case .deleteFeed:
            return executeFeedDeletion(operation)
        case .likeFeed:
            return executeFeedLike(operation)
        case .addComment:
            return executeCommentAddition(operation)
        case .updatePet:
            return executePetUpdate(operation)
        case .sendMessage:
            return executeMessageSending(operation)
        }
    }
    
    // MARK: - Operation Executors
    private func executeFeedCreation(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        do {
            let request = try JSONDecoder().decode(FeedCreateRequest.self, from: operation.data)
            return feedService.createFeed(request)
                .map { _ in
                    self.removePendingOperation(operation)
                    return true
                }
                .catch { error in
                    self.handleOperationError(operation, error: error)
                    return Just(false)
                }
                .eraseToAnyPublisher()
        } catch {
            removePendingOperation(operation)
            return Just(false).eraseToAnyPublisher()
        }
    }
    
    private func executeFeedUpdate(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        // 实现Feed更新逻辑
        return Just(true).eraseToAnyPublisher()
    }
    
    private func executeFeedDeletion(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        // 实现Feed删除逻辑
        return Just(true).eraseToAnyPublisher()
    }
    
    private func executeFeedLike(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        do {
            let data = try JSONSerialization.jsonObject(with: operation.data) as? [String: Any]
            guard let feedId = data?["feedId"] as? Int else {
                removePendingOperation(operation)
                return Just(false).eraseToAnyPublisher()
            }
            
            return feedService.likeFeed(feedId: feedId)
                .map { _ in
                    self.removePendingOperation(operation)
                    return true
                }
                .catch { error in
                    self.handleOperationError(operation, error: error)
                    return Just(false)
                }
                .eraseToAnyPublisher()
        } catch {
            removePendingOperation(operation)
            return Just(false).eraseToAnyPublisher()
        }
    }
    
    private func executeCommentAddition(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        // 实现评论添加逻辑
        return Just(true).eraseToAnyPublisher()
    }
    
    private func executePetUpdate(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        // 实现宠物更新逻辑
        return Just(true).eraseToAnyPublisher()
    }
    
    private func executeMessageSending(_ operation: PendingOperation) -> AnyPublisher<Bool, Never> {
        do {
            let request = try JSONDecoder().decode(AIRequest.self, from: operation.data)
            return aiService.sendMessage(request)
                .map { _ in
                    self.removePendingOperation(operation)
                    return true
                }
                .catch { error in
                    self.handleOperationError(operation, error: error)
                    return Just(false)
                }
                .eraseToAnyPublisher()
        } catch {
            removePendingOperation(operation)
            return Just(false).eraseToAnyPublisher()
        }
    }
    
    // MARK: - Error Handling
    private func handleOperationError(_ operation: PendingOperation, error: Error) {
        if operation.retryCount < maxRetryCount {
            // 增加重试次数并重新添加到队列
            let retryOperation = PendingOperation(
                operation: operation.operation,
                data: operation.data,
                retryCount: operation.retryCount + 1
            )
            
            var operations = getPendingOperations()
            if let index = operations.firstIndex(where: { $0.id == operation.id }) {
                operations[index] = retryOperation
                savePendingOperations(operations)
            }
        } else {
            // 超过最大重试次数，删除操作
            removePendingOperation(operation)
            print("❌ Operation failed after \(maxRetryCount) retries: \(operation.operation.rawValue)")
        }
    }
    
    private func removePendingOperation(_ operation: PendingOperation) {
        var operations = getPendingOperations()
        operations.removeAll { $0.id == operation.id }
        savePendingOperations(operations)
        pendingOperationsCount = operations.count
    }
    
    // MARK: - Full Data Sync
    func syncAllData() -> AnyPublisher<Bool, Never> {
        guard networkManager.isConnected else {
            return Just(false).eraseToAnyPublisher()
        }
        
        print("🔄 Starting full data sync...")
        
        let syncPublishers = [
            syncFeeds(),
            syncUserPets(),
            syncUserProfile()
        ]
        
        return Publishers.MergeMany(syncPublishers)
            .collect()
            .map { results in
                let allSuccess = results.allSatisfy { $0 }
                print(allSuccess ? "✅ Full sync completed" : "⚠️ Some sync operations failed")
                return allSuccess
            }
            .handleEvents(receiveOutput: { [weak self] _ in
                self?.lastSyncDate = Date()
            })
            .eraseToAnyPublisher()
    }
    
    private func syncFeeds() -> AnyPublisher<Bool, Never> {
        return feedService.getFeeds(page: 1, limit: 50)
            .map { feeds in
                self.cacheService.cacheFeeds(feeds)
                return true
            }
            .catch { _ in Just(false) }
            .eraseToAnyPublisher()
    }
    
    private func syncUserPets() -> AnyPublisher<Bool, Never> {
        guard let currentUser = AuthService.shared.currentUser else {
            return Just(false).eraseToAnyPublisher()
        }
        
        return petService.getUserPets(userId: currentUser.id)
            .map { pets in
                self.cacheService.cacheUserPets(pets)
                return true
            }
            .catch { _ in Just(false) }
            .eraseToAnyPublisher()
    }
    
    private func syncUserProfile() -> AnyPublisher<Bool, Never> {
        return AuthService.shared.getCurrentUser()
            .map { userResponse in
                if let user = userResponse.user {
                    self.cacheService.cacheCurrentUser(user)
                }
                return true
            }
            .catch { _ in Just(false) }
            .eraseToAnyPublisher()
    }
    
    // MARK: - Convenience Methods
    func clearPendingOperations() {
        cacheService.remove(forKey: pendingOperationsKey)
        pendingOperationsCount = 0
    }
    
    func getPendingOperationsCount() -> Int {
        return getPendingOperations().count
    }
}
