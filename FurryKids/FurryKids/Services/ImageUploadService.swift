import Foundation
import Combine
import UIKit

// MARK: - Image Upload Models
struct ImageUploadRequest {
    let imageData: Data
    let fileName: String
    let type: ImageType
    let compressionQuality: CGFloat
}

enum ImageType: String, CaseIterable {
    case avatar = "avatar"
    case feed = "feed"
    case pet = "pet"
    case background = "background"
}

struct ImageUploadResponse: Codable {
    let success: Bool
    let imageUrl: String?
    let thumbnailUrl: String?
    let message: String?
    
    enum CodingKeys: String, CodingKey {
        case success
        case imageUrl = "image_url"
        case thumbnailUrl = "thumbnail_url"
        case message
    }
}

// MARK: - Image Upload Service Protocol
protocol ImageUploadServiceProtocol {
    func uploadImage(_ request: ImageUploadRequest) -> AnyPublisher<ImageUploadResponse, APIError>
    func uploadImages(_ requests: [ImageUploadRequest]) -> AnyPublisher<[ImageUploadResponse], APIError>
    func deleteImage(imageUrl: String) -> AnyPublisher<Bool, APIError>
}

// MARK: - Image Upload Service Implementation
class ImageUploadService: ImageUploadServiceProtocol {
    static let shared = ImageUploadService()
    
    private let networkManager = NetworkManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Upload progress tracking
    @Published var uploadProgress: [String: Double] = [:]
    @Published var isUploading = false
    
    private init() {}
    
    // MARK: - Single Image Upload
    func uploadImage(_ request: ImageUploadRequest) -> AnyPublisher<ImageUploadResponse, APIError> {
        let uploadId = UUID().uuidString
        uploadProgress[uploadId] = 0.0
        isUploading = true
        
        return performImageUpload(request, uploadId: uploadId)
            .handleEvents(
                receiveOutput: { [weak self] _ in
                    self?.uploadProgress[uploadId] = 1.0
                    self?.isUploading = false
                },
                receiveCompletion: { [weak self] _ in
                    self?.uploadProgress.removeValue(forKey: uploadId)
                    self?.isUploading = false
                }
            )
            .eraseToAnyPublisher()
    }
    
    // MARK: - Multiple Images Upload
    func uploadImages(_ requests: [ImageUploadRequest]) -> AnyPublisher<[ImageUploadResponse], APIError> {
        isUploading = true
        
        let publishers = requests.enumerated().map { index, request in
            let uploadId = "batch_\(index)"
            uploadProgress[uploadId] = 0.0
            
            return performImageUpload(request, uploadId: uploadId)
                .handleEvents(receiveOutput: { [weak self] _ in
                    self?.uploadProgress[uploadId] = 1.0
                })
        }
        
        return Publishers.MergeMany(publishers)
            .collect()
            .handleEvents(
                receiveOutput: { [weak self] _ in
                    self?.isUploading = false
                },
                receiveCompletion: { [weak self] _ in
                    self?.uploadProgress.removeAll()
                    self?.isUploading = false
                }
            )
            .eraseToAnyPublisher()
    }
    
    // MARK: - Delete Image
    func deleteImage(imageUrl: String) -> AnyPublisher<Bool, APIError> {
        let requestData = ["image_url": imageUrl]
        
        guard let data = try? JSONSerialization.data(withJSONObject: requestData) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return networkManager.request(
            endpoint: "/api/upload/delete",
            method: .DELETE,
            body: data,
            responseType: [String: Bool].self
        )
        .map { response in
            return response["success"] ?? false
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Private Upload Implementation
    private func performImageUpload(_ request: ImageUploadRequest, uploadId: String) -> AnyPublisher<ImageUploadResponse, APIError> {
        guard let url = URL(string: APIConfig.baseURL + "/api/upload/image") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        
        // 添加认证头
        if let token = APIConfig.authToken {
            urlRequest.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        let boundary = UUID().uuidString
        urlRequest.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        let body = createMultipartBody(
            boundary: boundary,
            imageData: request.imageData,
            fileName: request.fileName,
            imageType: request.type
        )
        urlRequest.httpBody = body
        
        return URLSession.shared.dataTaskPublisher(for: urlRequest)
            .tryMap { data, response in
                // 检查HTTP状态码
                if let httpResponse = response as? HTTPURLResponse {
                    switch httpResponse.statusCode {
                    case 401:
                        throw APIError.unauthorized
                    case 400...499:
                        throw APIError.serverError(httpResponse.statusCode)
                    case 500...599:
                        throw APIError.serverError(httpResponse.statusCode)
                    default:
                        break
                    }
                }
                return data
            }
            .decode(type: ImageUploadResponse.self, decoder: JSONDecoder())
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError(error)
                }
            }
            .eraseToAnyPublisher()
    }
    
    // MARK: - Multipart Body Creation
    private func createMultipartBody(boundary: String, imageData: Data, fileName: String, imageType: ImageType) -> Data {
        var body = Data()
        
        // 添加图片数据
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"image\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/jpeg\r\n\r\n".data(using: .utf8)!)
        body.append(imageData)
        body.append("\r\n".data(using: .utf8)!)
        
        // 添加图片类型
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"type\"\r\n\r\n".data(using: .utf8)!)
        body.append(imageType.rawValue.data(using: .utf8)!)
        body.append("\r\n".data(using: .utf8)!)
        
        // 结束边界
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        return body
    }
    
    // MARK: - Image Processing Utilities
    func compressImage(_ image: UIImage, quality: CGFloat = 0.8) -> Data? {
        return image.jpegData(compressionQuality: quality)
    }
    
    func resizeImage(_ image: UIImage, maxSize: CGSize) -> UIImage {
        let size = image.size
        
        let widthRatio = maxSize.width / size.width
        let heightRatio = maxSize.height / size.height
        let ratio = min(widthRatio, heightRatio)
        
        let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return newImage ?? image
    }
    
    func createThumbnail(_ image: UIImage, size: CGSize = CGSize(width: 200, height: 200)) -> UIImage {
        return resizeImage(image, maxSize: size)
    }
    
    // MARK: - Convenience Methods
    func uploadFeedImage(_ image: UIImage, compressionQuality: CGFloat = 0.8) -> AnyPublisher<ImageUploadResponse, APIError> {
        let resizedImage = resizeImage(image, maxSize: CGSize(width: 1080, height: 1080))
        
        guard let imageData = compressImage(resizedImage, quality: compressionQuality) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        let request = ImageUploadRequest(
            imageData: imageData,
            fileName: "feed_\(Date().timeIntervalSince1970).jpg",
            type: .feed,
            compressionQuality: compressionQuality
        )
        
        return uploadImage(request)
    }
    
    func uploadAvatarImage(_ image: UIImage, compressionQuality: CGFloat = 0.9) -> AnyPublisher<ImageUploadResponse, APIError> {
        let resizedImage = resizeImage(image, maxSize: CGSize(width: 400, height: 400))
        
        guard let imageData = compressImage(resizedImage, quality: compressionQuality) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        let request = ImageUploadRequest(
            imageData: imageData,
            fileName: "avatar_\(Date().timeIntervalSince1970).jpg",
            type: .avatar,
            compressionQuality: compressionQuality
        )
        
        return uploadImage(request)
    }
    
    func uploadPetImage(_ image: UIImage, compressionQuality: CGFloat = 0.8) -> AnyPublisher<ImageUploadResponse, APIError> {
        let resizedImage = resizeImage(image, maxSize: CGSize(width: 800, height: 800))
        
        guard let imageData = compressImage(resizedImage, quality: compressionQuality) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        let request = ImageUploadRequest(
            imageData: imageData,
            fileName: "pet_\(Date().timeIntervalSince1970).jpg",
            type: .pet,
            compressionQuality: compressionQuality
        )
        
        return uploadImage(request)
    }
    
    // MARK: - Progress Tracking
    func getUploadProgress() -> Double {
        guard !uploadProgress.isEmpty else { return 0.0 }
        let totalProgress = uploadProgress.values.reduce(0, +)
        return totalProgress / Double(uploadProgress.count)
    }
    
    func clearProgress() {
        uploadProgress.removeAll()
        isUploading = false
    }
}
