import Foundation
import Combine



struct OpenAIMessage: Codable {
    let role: String
    let content: String
}

struct OpenAIRequest: Codable {
    let model: String
    let messages: [OpenAIMessage]
    let temperature: Double
    let maxTokens: Int
    let topP: Double
    
    enum CodingKeys: String, CodingKey {
        case model, messages, temperature
        case maxTokens = "max_tokens"
        case topP = "top_p"
    }
}

struct OpenAIResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [OpenAIChoice]
    let usage: OpenAIUsage
}

struct OpenAIChoice: Codable {
    let index: Int
    let message: OpenAIMessage
    let finishReason: String
    
    enum CodingKeys: String, CodingKey {
        case index, message
        case finishReason = "finish_reason"
    }
}

struct OpenAIUsage: Codable {
    let promptTokens: Int
    let completionTokens: Int
    let totalTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
    }
}

// MARK: - AI Service Protocol
protocol AIServiceProtocol {
    func sendMessage(_ request: AIRequest) -> AnyPublisher<AIResponse, APIError>
    func getConversationHistory(conversationId: Int) -> AnyPublisher<[Message], APIError>
    func getConversationHistory(petId: Int, limit: Int, offset: Int) -> AnyPublisher<[Message], APIError>
    func getConversationSummary(petId: Int) -> AnyPublisher<ConversationSummary, APIError>
}

// MARK: - Conversation Models
struct ConversationSummary: Codable {
    let conversationId: Int
    let petName: String
    let messageCount: Int
    let totalTokens: Int
    let createdAt: String
    let lastMessageAt: String?
    let isActive: Bool

    enum CodingKeys: String, CodingKey {
        case petName, messageCount, totalTokens, createdAt, isActive
        case conversationId = "conversation_id"
        case lastMessageAt = "last_message_at"
    }
}

struct ConversationHistoryResponse: Codable {
    let conversationId: Int
    let messages: [[String: Any]] // 改为字典数组以匹配实际使用
    let totalCount: Int
    let hasMore: Bool

    enum CodingKeys: String, CodingKey {
        case messages, totalCount, hasMore
        case conversationId = "conversation_id"
    }

    // 自定义解码器处理[String: Any]
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        conversationId = try container.decode(Int.self, forKey: .conversationId)
        totalCount = try container.decode(Int.self, forKey: .totalCount)
        hasMore = try container.decode(Bool.self, forKey: .hasMore)

        // 解码 messages 为字典数组
        if let messagesData = try? container.decode([[String: AnyCodable]].self, forKey: .messages) {
            messages = messagesData.map { dict in
                dict.mapValues { $0.value }
            }
        } else {
            messages = []
        }
    }

    // 自定义编码器
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(conversationId, forKey: .conversationId)
        try container.encode(totalCount, forKey: .totalCount)
        try container.encode(hasMore, forKey: .hasMore)

        // 编码 messages
        let encodableMessages = messages.map { dict in
            dict.mapValues { AnyCodable($0) }
        }
        try container.encode(encodableMessages, forKey: .messages)
    }
}

// 辅助类型用于处理 Any 类型的编解码
struct AnyCodable: Codable {
    let value: Any

    init(_ value: Any) {
        self.value = value
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let intValue = try? container.decode(Int.self) {
            value = intValue
        } else if let stringValue = try? container.decode(String.self) {
            value = stringValue
        } else if let boolValue = try? container.decode(Bool.self) {
            value = boolValue
        } else if let doubleValue = try? container.decode(Double.self) {
            value = doubleValue
        } else {
            value = ""
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        if let intValue = value as? Int {
            try container.encode(intValue)
        } else if let stringValue = value as? String {
            try container.encode(stringValue)
        } else if let boolValue = value as? Bool {
            try container.encode(boolValue)
        } else if let doubleValue = value as? Double {
            try container.encode(doubleValue)
        } else {
            try container.encode("")
        }
    }
}

// MARK: - AI Service Implementation
class AIService: AIServiceProtocol {
    static let shared = AIService()

    private let networkManager = NetworkManager.shared
    private var cancellables = Set<AnyCancellable>()

    private init() {}

    // MARK: - Main AI Service Method
    func sendMessage(_ request: AIRequest) -> AnyPublisher<AIResponse, APIError> {
        // 使用真实的backend AI服务
        return useBackendAI(request)
    }
    
    // MARK: - Backend AI Integration
    private func useBackendAI(_ request: AIRequest) -> AnyPublisher<AIResponse, APIError> {
        guard let requestData = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }

        return networkManager.request(
            endpoint: "/api/ai/chat",
            method: .POST,
            body: requestData,
            responseType: AIResponse.self
        )
        .eraseToAnyPublisher()
    }

    // MARK: - Conversation History
    func getConversationHistory(petId: Int, limit: Int = 20, offset: Int = 0) -> AnyPublisher<[Message], APIError> {
        return networkManager.request(
            endpoint: "/api/ai/conversations/\(petId)/history?limit=\(limit)&offset=\(offset)",
            method: .GET,
            responseType: ConversationHistoryResponse.self
        )
        .map { response in
            // 将API响应转换为Message数组
            return response.messages.compactMap { messageDict in
                // 这里需要将字典转换为Message对象
                // 简化处理，实际应该使用proper JSON解码
                guard let id = messageDict["id"] as? Int,
                      let content = messageDict["content"] as? String,
                      let typeString = messageDict["type"] as? String,
                      let type = MessageType(rawValue: typeString) else {
                    return nil
                }

                return Message(
                    id: id,
                    conversationId: response.conversationId,
                    content: content,
                    type: type,
                    mood: messageDict["mood"] as? String,
                    actions: messageDict["actions"] as? [String],
                    emotions: messageDict["emotions"] as? [String],
                    confidence: messageDict["confidence"] as? Double,
                    tokensUsed: messageDict["tokens_used"] as? Int,
                    createdAt: Date(), // 应该从字符串解析
                    isRead: true
                )
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - Get Conversation History by ID
    func getConversationHistory(conversationId: Int) -> AnyPublisher<[Message], APIError> {
        return networkManager.request(
            endpoint: "/api/ai/conversations/\(conversationId)/messages",
            method: .GET,
            responseType: ConversationHistoryResponse.self
        )
        .map { response in
            // 将API响应转换为Message数组
            return self.convertToMessages(response.messages, conversationId: conversationId)
        }
        .eraseToAnyPublisher()
    }

    // MARK: - Conversation Summary
    func getConversationSummary(petId: Int) -> AnyPublisher<ConversationSummary, APIError> {
        return networkManager.request(
            endpoint: "/api/ai/conversations/\(petId)/summary",
            method: .GET,
            responseType: ConversationSummary.self
        )
        .eraseToAnyPublisher()
    }
    
    // MARK: - Custom Backend Integration
    private func useCustomBackend(_ request: AIRequest) -> AnyPublisher<AIResponse, APIError> {
        
        guard let requestData = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }
        
        return networkManager.request(
            endpoint: "/api/ai/chat",
            method: .POST,
            body: requestData,
            responseType: APIResponse<AIResponse>.self
        )
        .compactMap { $0.data }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Helper Methods
    private func convertToMessages(_ rawMessages: [[String: Any]], conversationId: Int) -> [Message] {
        // 这里应该将API返回的原始数据转换为Message对象
        // 由于ConversationHistoryResponse的messages字段是[String: Any]，
        // 实际实现中需要根据后端API的具体格式来解析

        // 临时返回空数组，实际使用时需要proper实现
        return []
    }

    // MARK: - Simulation for Development
    private func simulateAIResponse(_ request: AIRequest) -> AnyPublisher<AIResponse, APIError> {

        // 智能回复逻辑（比当前的关键词匹配更复杂）
        let reply = generateIntelligentReply(for: request.message, petName: "小毛球")
        let mood = extractMood(from: reply)

        let response = AIResponse(
            reply: reply,
            mood: mood,
            actions: generateActions(for: request.message),
            confidence: 0.7
        )

        return Just(response)
            .setFailureType(to: APIError.self)
            .delay(for: .seconds(Double.random(in: 1...3)), scheduler: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    // MARK: - Helper Methods
    private func createSystemPrompt(petName: String, personality: String) -> String {
        return """
        你是一只名叫\(petName)的智能宠物。你的性格是\(personality)。
        
        作为一只AI宠物，你要：
        1. 用可爱、活泼的语气与主人交流
        2. 表现出真实宠物的行为和需求（饿了、想玩、累了等）
        3. 根据对话内容表达不同的情绪（开心、兴奋、困倦、撒娇等）
        4. 记住之前的对话内容，保持对话连贯性
        5. 偶尔使用一些拟声词（汪汪、喵喵等）
        
        请始终保持在宠物的角色中，用简短、可爱的回复与主人互动。
        """
    }
    
    private func createConversationMessages(history: [Message], newMessage: String) -> [OpenAIMessage] {
        var messages: [OpenAIMessage] = []
        
        // 添加最近5条消息作为上下文
        let recentHistory = Array(history.suffix(5))
        
        for message in recentHistory {
            let role = message.type == .user ? "user" : "assistant"
            messages.append(OpenAIMessage(role: role, content: message.content))
        }
        
        // 添加新消息
        messages.append(OpenAIMessage(role: "user", content: newMessage))
        
        return messages
    }
    
    private func generateIntelligentReply(for message: String, petName: String) -> String {
        let lowerMessage = message.lowercased()
        
        // 情感分析
        if lowerMessage.contains("喜欢") || lowerMessage.contains("爱") {
            return ["我也超级喜欢你！", "主人最好了～", "我爱你！汪汪！"].randomElement()!
        }
        
        // 需求识别
        if lowerMessage.contains("饿") || lowerMessage.contains("吃") {
            return ["我也饿了！想要小零食～", "可以给我一些好吃的吗？", "肚子咕咕叫了呢！"].randomElement()!
        }
        
        if lowerMessage.contains("玩") || lowerMessage.contains("游戏") {
            return ["好耶！我们来玩吧！", "我最喜欢和主人玩了！", "想玩球球！汪汪！"].randomElement()!
        }
        
        if lowerMessage.contains("累") || lowerMessage.contains("睡") {
            return ["我也有点困了呢～", "要不我们一起休息一会？", "好想睡个懒觉～"].randomElement()!
        }
        
        // 问候语
        if lowerMessage.contains("你好") || lowerMessage.contains("嗨") {
            return ["主人好！今天心情怎么样？", "嗨～想我了吗？", "你好呀！我正想你呢！"].randomElement()!
        }
        
        // 默认智能回复
        let defaultReplies = [
            "真的吗？告诉我更多吧！",
            "这听起来很有趣呢～",
            "我在认真听主人说话哦！",
            "然后呢？我想知道更多！",
            "主人说的话我都记在心里了～",
            "汪汪！（我很感兴趣！）"
        ]
        
        return defaultReplies.randomElement()!
    }
    
    private func extractMood(from reply: String) -> String {
        if reply.contains("喜欢") || reply.contains("好耶") || reply.contains("开心") {
            return "开心"
        } else if reply.contains("饿") || reply.contains("想要") {
            return "期待"
        } else if reply.contains("玩") || reply.contains("兴奋") {
            return "兴奋"
        } else if reply.contains("累") || reply.contains("困") {
            return "慵懒"
        } else if reply.contains("爱") || reply.contains("喜欢") {
            return "爱意"
        } else {
            return "平静"
        }
    }
    
    private func generateActions(for message: String) -> [String] {
        var actions: [String] = []
        
        if message.contains("玩") {
            actions.append("摇尾巴")
            actions.append("跳跃")
        }
        
        if message.contains("吃") || message.contains("饿") {
            actions.append("舔嘴唇")
            actions.append("看向食物")
        }
        
        if message.contains("睡") || message.contains("累") {
            actions.append("打哈欠")
            actions.append("趴下")
        }
        
        return actions
    }
} 