import Foundation
import Combine

// MARK: - Testing Service
class TestingService: ObservableObject {
    static let shared = TestingService()
    
    private let authService = AuthService.shared
    private let feedService = FeedService.shared
    private let petService = PetService.shared
    private let aiService = AIService.shared
    private let imageUploadService = ImageUploadService.shared
    private let cacheService = CacheService.shared
    private let dataSyncService = DataSyncService.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    @Published var testResults: [TestResult] = []
    @Published var isRunningTests = false
    
    struct TestResult {
        let testName: String
        let success: Bool
        let message: String
        let duration: TimeInterval
        let timestamp: Date
    }
    
    private init() {}
    
    // MARK: - Test Suite Runner
    func runAllTests() {
        guard !isRunningTests else { return }
        
        isRunningTests = true
        testResults.removeAll()
        
        print("🧪 Starting comprehensive integration tests...")
        
        let tests: [() -> AnyPublisher<TestResult, Never>] = [
            testNetworkConnectivity,
            testAuthentication,
            testPetManagement,
            testFeedOperations,
            testAIIntegration,
            testImageUpload,
            testCacheOperations,
            testOfflineSync,
            testErrorHandling
        ]
        
        // 顺序执行测试
        tests.reduce(Just(()).setFailureType(to: Never.self).eraseToAnyPublisher()) { acc, test in
            acc.flatMap { _ in test() }
                .handleEvents(receiveOutput: { [weak self] result in
                    self?.testResults.append(result)
                    print(result.success ? "✅" : "❌", result.testName, "-", result.message)
                })
                .map { _ in () }
                .eraseToAnyPublisher()
        }
        .sink(receiveCompletion: { [weak self] _ in
            self?.isRunningTests = false
            self?.printTestSummary()
        }, receiveValue: { _ in })
        .store(in: &cancellables)
    }
    
    // MARK: - Individual Tests
    private func testNetworkConnectivity() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        return authService.healthCheck()
            .map { _ in
                TestResult(
                    testName: "Network Connectivity",
                    success: true,
                    message: "Backend server is reachable",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                )
            }
            .catch { error in
                Just(TestResult(
                    testName: "Network Connectivity",
                    success: false,
                    message: "Failed to connect to backend: \(error.localizedDescription)",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                ))
            }
            .eraseToAnyPublisher()
    }
    
    private func testAuthentication() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        let testUsername = "test_user_\(Int.random(in: 1000...9999))"
        let testPassword = "test_password_123"
        
        return authService.register(username: testUsername, password: testPassword)
            .flatMap { _ in
                self.authService.login(username: testUsername, password: testPassword)
            }
            .flatMap { _ in
                self.authService.getCurrentUser()
            }
            .map { _ in
                TestResult(
                    testName: "Authentication Flow",
                    success: true,
                    message: "Register, login, and user info retrieval successful",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                )
            }
            .catch { error in
                Just(TestResult(
                    testName: "Authentication Flow",
                    success: false,
                    message: "Authentication failed: \(error.localizedDescription)",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                ))
            }
            .eraseToAnyPublisher()
    }
    
    private func testPetManagement() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        guard let currentUser = authService.currentUser else {
            return Just(TestResult(
                testName: "Pet Management",
                success: false,
                message: "No authenticated user",
                duration: 0,
                timestamp: Date()
            )).eraseToAnyPublisher()
        }
        
        let petRequest = PetCreateRequest(
            name: "Test Pet",
            type: "dog",
            breed: "Golden Retriever",
            personality: "friendly,playful",
            avatar: "🐕",
            birthday: Calendar.current.date(byAdding: .year, value: -2, to: Date()),
            weight: 25.0,
            healthStatus: "healthy"
        )
        
        return petService.createPet(petRequest)
            .flatMap { pet in
                self.petService.getUserPets(userId: currentUser.id)
                    .map { pets in (pet, pets) }
            }
            .flatMap { (createdPet, pets) in
                self.petService.deletePet(petId: createdPet.id)
                    .map { success in (createdPet, pets, success) }
            }
            .map { (createdPet, pets, deleteSuccess) in
                let hasCreatedPet = pets.contains { $0.id == createdPet.id }
                let success = hasCreatedPet && deleteSuccess
                
                return TestResult(
                    testName: "Pet Management",
                    success: success,
                    message: success ? "Pet CRUD operations successful" : "Pet operations failed",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                )
            }
            .catch { error in
                Just(TestResult(
                    testName: "Pet Management",
                    success: false,
                    message: "Pet management failed: \(error.localizedDescription)",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                ))
            }
            .eraseToAnyPublisher()
    }
    
    private func testFeedOperations() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        // 首先确保有宠物
        guard let currentUser = authService.currentUser else {
            return Just(TestResult(
                testName: "Feed Operations",
                success: false,
                message: "No authenticated user",
                duration: 0,
                timestamp: Date()
            )).eraseToAnyPublisher()
        }
        
        return petService.getUserPets(userId: currentUser.id)
            .flatMap { pets -> AnyPublisher<TestResult, Never> in
                guard let firstPet = pets.first else {
                    return Just(TestResult(
                        testName: "Feed Operations",
                        success: false,
                        message: "No pets available for testing",
                        duration: Date().timeIntervalSince(startTime),
                        timestamp: Date()
                    )).eraseToAnyPublisher()
                }
                
                let feedRequest = FeedCreateRequest(
                    petId: firstPet.id,
                    content: "Test feed content",
                    images: [],
                    tags: ["test"],
                    mood: "happy",
                    location: nil,
                    visibility: "public"
                )
                
                return self.feedService.createFeed(feedRequest)
                    .flatMap { feed in
                        self.feedService.likeFeed(feedId: feed.id)
                            .map { _ in feed }
                    }
                    .flatMap { feed in
                        self.feedService.getFeeds(page: 1, limit: 10)
                            .map { feeds in (feed, feeds) }
                    }
                    .flatMap { (createdFeed, feeds) in
                        self.feedService.deleteFeed(feedId: createdFeed.id)
                            .map { success in (createdFeed, feeds, success) }
                    }
                    .map { (createdFeed, feeds, deleteSuccess) in
                        let hasCreatedFeed = feeds.contains { $0.id == createdFeed.id }
                        let success = hasCreatedFeed && deleteSuccess
                        
                        return TestResult(
                            testName: "Feed Operations",
                            success: success,
                            message: success ? "Feed CRUD and like operations successful" : "Feed operations failed",
                            duration: Date().timeIntervalSince(startTime),
                            timestamp: Date()
                        )
                    }
                    .catch { error in
                        Just(TestResult(
                            testName: "Feed Operations",
                            success: false,
                            message: "Feed operations failed: \(error.localizedDescription)",
                            duration: Date().timeIntervalSince(startTime),
                            timestamp: Date()
                        ))
                    }
                    .eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }
    
    private func testAIIntegration() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        guard let currentUser = authService.currentUser else {
            return Just(TestResult(
                testName: "AI Integration",
                success: false,
                message: "No authenticated user",
                duration: 0,
                timestamp: Date()
            )).eraseToAnyPublisher()
        }
        
        return petService.getUserPets(userId: currentUser.id)
            .flatMap { pets -> AnyPublisher<TestResult, Never> in
                guard let firstPet = pets.first else {
                    return Just(TestResult(
                        testName: "AI Integration",
                        success: false,
                        message: "No pets available for AI testing",
                        duration: Date().timeIntervalSince(startTime),
                        timestamp: Date()
                    )).eraseToAnyPublisher()
                }
                
                let aiRequest = AIRequest(
                    message: "Hello, how are you today?",
                    petId: firstPet.id,
                    contextData: ["test": "true"]
                )
                
                return self.aiService.sendMessage(aiRequest)
                    .map { response in
                        let success = !response.reply.isEmpty && response.confidence > 0
                        return TestResult(
                            testName: "AI Integration",
                            success: success,
                            message: success ? "AI chat response received successfully" : "AI response was invalid",
                            duration: Date().timeIntervalSince(startTime),
                            timestamp: Date()
                        )
                    }
                    .catch { error in
                        Just(TestResult(
                            testName: "AI Integration",
                            success: false,
                            message: "AI integration failed: \(error.localizedDescription)",
                            duration: Date().timeIntervalSince(startTime),
                            timestamp: Date()
                        ))
                    }
                    .eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }
    
    private func testImageUpload() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        // 创建一个测试图片
        let testImage = createTestImage()
        
        return imageUploadService.uploadAvatarImage(testImage)
            .map { response in
                let success = response.success && response.imageUrl != nil
                return TestResult(
                    testName: "Image Upload",
                    success: success,
                    message: success ? "Image upload successful" : "Image upload failed",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                )
            }
            .catch { error in
                Just(TestResult(
                    testName: "Image Upload",
                    success: false,
                    message: "Image upload failed: \(error.localizedDescription)",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                ))
            }
            .eraseToAnyPublisher()
    }
    
    private func testCacheOperations() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        // 测试缓存操作
        let testData = ["test": "data"]
        let testKey = "test_cache_key"
        
        cacheService.store(testData, forKey: testKey, expiration: 60)
        let retrievedData: [String: String]? = cacheService.retrieve([String: String].self, forKey: testKey)
        
        let success = retrievedData?["test"] == "data"
        
        // 清理测试数据
        cacheService.remove(forKey: testKey)
        
        return Just(TestResult(
            testName: "Cache Operations",
            success: success,
            message: success ? "Cache store and retrieve successful" : "Cache operations failed",
            duration: Date().timeIntervalSince(startTime),
            timestamp: Date()
        )).eraseToAnyPublisher()
    }
    
    private func testOfflineSync() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        // 测试离线同步功能
        let testOperation = PendingOperation(
            operation: .likeFeed,
            data: try! JSONSerialization.data(withJSONObject: ["feedId": 1])
        )
        
        dataSyncService.addPendingOperation(testOperation)
        let pendingCount = dataSyncService.getPendingOperationsCount()
        
        return dataSyncService.syncPendingOperations()
            .map { success in
                TestResult(
                    testName: "Offline Sync",
                    success: pendingCount > 0,
                    message: "Offline sync operations tested (pending: \(pendingCount))",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                )
            }
            .eraseToAnyPublisher()
    }
    
    private func testErrorHandling() -> AnyPublisher<TestResult, Never> {
        let startTime = Date()
        
        // 测试错误处理 - 尝试访问不存在的资源
        return feedService.deleteFeed(feedId: 999999)
            .map { _ in
                TestResult(
                    testName: "Error Handling",
                    success: false,
                    message: "Expected error but operation succeeded",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                )
            }
            .catch { error in
                let success = error.localizedDescription.contains("404") || error.localizedDescription.contains("not found")
                return Just(TestResult(
                    testName: "Error Handling",
                    success: success,
                    message: success ? "Error handling working correctly" : "Unexpected error: \(error.localizedDescription)",
                    duration: Date().timeIntervalSince(startTime),
                    timestamp: Date()
                ))
            }
            .eraseToAnyPublisher()
    }
    
    // MARK: - Helper Methods
    private func createTestImage() -> UIImage {
        let size = CGSize(width: 100, height: 100)
        UIGraphicsBeginImageContext(size)
        UIColor.blue.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()
        return image
    }
    
    private func printTestSummary() {
        let totalTests = testResults.count
        let passedTests = testResults.filter { $0.success }.count
        let failedTests = totalTests - passedTests
        let totalDuration = testResults.reduce(0) { $0 + $1.duration }
        
        print("\n📊 Test Summary:")
        print("Total Tests: \(totalTests)")
        print("Passed: \(passedTests) ✅")
        print("Failed: \(failedTests) ❌")
        print("Success Rate: \(String(format: "%.1f", Double(passedTests) / Double(totalTests) * 100))%")
        print("Total Duration: \(String(format: "%.2f", totalDuration))s")
        print("Average Duration: \(String(format: "%.2f", totalDuration / Double(totalTests)))s")
    }
    
    // MARK: - Quick Tests
    func quickConnectivityTest() -> AnyPublisher<Bool, Never> {
        return authService.healthCheck()
            .map { _ in true }
            .catch { _ in Just(false) }
            .eraseToAnyPublisher()
    }
    
    func clearTestData() {
        testResults.removeAll()
        cacheService.clearAll()
        dataSyncService.clearPendingOperations()
    }
}
