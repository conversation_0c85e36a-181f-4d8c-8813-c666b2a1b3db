import Foundation
import Combine

// MARK: - Cache Service Protocol
protocol CacheServiceProtocol {
    func store<T: Codable>(_ object: T, forKey key: String, expiration: TimeInterval?)
    func retrieve<T: Codable>(_ type: T.Type, forKey key: String) -> T?
    func remove(forKey key: String)
    func clearAll()
    func isExpired(forKey key: String) -> Bool
    func cacheSize() -> Int64
}

// MARK: - Cache Entry
private struct CacheEntry<T: Codable>: Codable {
    let object: T
    let timestamp: Date
    let expiration: TimeInterval?
    
    var isExpired: Bool {
        guard let expiration = expiration else { return false }
        return Date().timeIntervalSince(timestamp) > expiration
    }
}

// MARK: - Cache Service Implementation
class CacheService: CacheServiceProtocol {
    static let shared = CacheService()
    
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let maxCacheSize: Int64 = 100 * 1024 * 1024 // 100MB
    
    // Cache keys
    struct CacheKeys {
        static let feeds = "cached_feeds"
        static let userPets = "cached_user_pets"
        static let currentUser = "cached_current_user"
        static let conversations = "cached_conversations"
        static let petStats = "cached_pet_stats"
        static let feedComments = "cached_feed_comments"
    }
    
    private init() {
        // 创建缓存目录
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        cacheDirectory = documentsPath.appendingPathComponent("FurryKidsCache")
        
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        }
        
        // 启动时清理过期缓存
        cleanupExpiredCache()
    }
    
    // MARK: - Core Cache Operations
    func store<T: Codable>(_ object: T, forKey key: String, expiration: TimeInterval? = nil) {
        let entry = CacheEntry(object: object, timestamp: Date(), expiration: expiration)
        let url = cacheDirectory.appendingPathComponent("\(key).cache")
        
        do {
            let data = try JSONEncoder().encode(entry)
            try data.write(to: url)
            
            // 检查缓存大小
            manageCacheSize()
        } catch {
            print("Failed to store cache for key \(key): \(error)")
        }
    }
    
    func retrieve<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        let url = cacheDirectory.appendingPathComponent("\(key).cache")
        
        guard fileManager.fileExists(atPath: url.path) else { return nil }
        
        do {
            let data = try Data(contentsOf: url)
            let entry = try JSONDecoder().decode(CacheEntry<T>.self, from: data)
            
            if entry.isExpired {
                remove(forKey: key)
                return nil
            }
            
            return entry.object
        } catch {
            print("Failed to retrieve cache for key \(key): \(error)")
            remove(forKey: key) // 删除损坏的缓存
            return nil
        }
    }
    
    func remove(forKey key: String) {
        let url = cacheDirectory.appendingPathComponent("\(key).cache")
        try? fileManager.removeItem(at: url)
    }
    
    func clearAll() {
        do {
            let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for url in contents {
                try fileManager.removeItem(at: url)
            }
        } catch {
            print("Failed to clear cache: \(error)")
        }
    }
    
    func isExpired(forKey key: String) -> Bool {
        let url = cacheDirectory.appendingPathComponent("\(key).cache")
        
        guard fileManager.fileExists(atPath: url.path) else { return true }
        
        do {
            let data = try Data(contentsOf: url)
            let entry = try JSONDecoder().decode(CacheEntry<Data>.self, from: data)
            return entry.isExpired
        } catch {
            return true
        }
    }
    
    func cacheSize() -> Int64 {
        do {
            let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            return contents.reduce(0) { total, url in
                let size = (try? url.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0
                return total + Int64(size)
            }
        } catch {
            return 0
        }
    }
    
    // MARK: - Cache Management
    private func cleanupExpiredCache() {
        do {
            let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.creationDateKey])
            
            for url in contents {
                let key = url.deletingPathExtension().lastPathComponent
                if isExpired(forKey: key) {
                    try fileManager.removeItem(at: url)
                }
            }
        } catch {
            print("Failed to cleanup expired cache: \(error)")
        }
    }
    
    private func manageCacheSize() {
        let currentSize = cacheSize()
        
        if currentSize > maxCacheSize {
            // 删除最旧的缓存文件
            do {
                let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.creationDateKey])
                let sortedContents = contents.sorted { url1, url2 in
                    let date1 = (try? url1.resourceValues(forKeys: [.creationDateKey]))?.creationDate ?? Date.distantPast
                    let date2 = (try? url2.resourceValues(forKeys: [.creationDateKey]))?.creationDate ?? Date.distantPast
                    return date1 < date2
                }
                
                // 删除最旧的文件直到缓存大小合适
                var deletedSize: Int64 = 0
                let targetSize = maxCacheSize * 3 / 4 // 删除到75%
                
                for url in sortedContents {
                    let fileSize = (try? url.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0
                    try fileManager.removeItem(at: url)
                    deletedSize += Int64(fileSize)
                    
                    if currentSize - deletedSize <= targetSize {
                        break
                    }
                }
            } catch {
                print("Failed to manage cache size: \(error)")
            }
        }
    }
    
    // MARK: - Convenience Methods for App Data
    func cacheFeeds(_ feeds: [Feed], expiration: TimeInterval = 3600) { // 1小时过期
        store(feeds, forKey: CacheKeys.feeds, expiration: expiration)
    }
    
    func getCachedFeeds() -> [Feed]? {
        return retrieve([Feed].self, forKey: CacheKeys.feeds)
    }
    
    func cacheUserPets(_ pets: [Pet], expiration: TimeInterval = 7200) { // 2小时过期
        store(pets, forKey: CacheKeys.userPets, expiration: expiration)
    }
    
    func getCachedUserPets() -> [Pet]? {
        return retrieve([Pet].self, forKey: CacheKeys.userPets)
    }
    
    func cacheCurrentUser(_ user: User, expiration: TimeInterval = 86400) { // 24小时过期
        store(user, forKey: CacheKeys.currentUser, expiration: expiration)
    }
    
    func getCachedCurrentUser() -> User? {
        return retrieve(User.self, forKey: CacheKeys.currentUser)
    }
    
    func cacheConversations(_ conversations: [Conversation], forPetId petId: Int, expiration: TimeInterval = 1800) { // 30分钟过期
        let key = "\(CacheKeys.conversations)_\(petId)"
        store(conversations, forKey: key, expiration: expiration)
    }
    
    func getCachedConversations(forPetId petId: Int) -> [Conversation]? {
        let key = "\(CacheKeys.conversations)_\(petId)"
        return retrieve([Conversation].self, forKey: key)
    }
    
    func cachePetStats(_ stats: PetStats, forPetId petId: Int, expiration: TimeInterval = 3600) { // 1小时过期
        let key = "\(CacheKeys.petStats)_\(petId)"
        store(stats, forKey: key, expiration: expiration)
    }
    
    func getCachedPetStats(forPetId petId: Int) -> PetStats? {
        let key = "\(CacheKeys.petStats)_\(petId)"
        return retrieve(PetStats.self, forKey: key)
    }
    
    func cacheFeedComments(_ comments: [FeedComment], forFeedId feedId: Int, expiration: TimeInterval = 1800) { // 30分钟过期
        let key = "\(CacheKeys.feedComments)_\(feedId)"
        store(comments, forKey: key, expiration: expiration)
    }
    
    func getCachedFeedComments(forFeedId feedId: Int) -> [FeedComment]? {
        let key = "\(CacheKeys.feedComments)_\(feedId)"
        return retrieve([FeedComment].self, forKey: key)
    }
    
    // MARK: - Cache Statistics
    func getCacheStatistics() -> [String: Any] {
        let size = cacheSize()
        let sizeInMB = Double(size) / (1024 * 1024)
        
        do {
            let contents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            return [
                "totalFiles": contents.count,
                "totalSize": size,
                "totalSizeMB": String(format: "%.2f", sizeInMB),
                "maxSizeMB": Double(maxCacheSize) / (1024 * 1024),
                "cacheDirectory": cacheDirectory.path
            ]
        } catch {
            return [
                "error": error.localizedDescription
            ]
        }
    }
}
