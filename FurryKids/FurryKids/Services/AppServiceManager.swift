import Foundation
import Combine
import SwiftUI

// MARK: - App Service Manager
class AppServiceManager: ObservableObject {
    static let shared = AppServiceManager()
    
    // Service instances
    let authService = AuthService.shared
    let networkManager = NetworkManager.shared
    let feedService = FeedService.shared
    let petService = PetService.shared
    let aiService = AIService.shared
    let imageUploadService = ImageUploadService.shared
    let cacheService = CacheService.shared
    let dataSyncService = DataSyncService.shared
    let testingService = TestingService.shared
    
    private var cancellables = Set<AnyCancellable>()
    
    // App state
    @Published var isInitialized = false
    @Published var isOnline = true
    @Published var currentError: AppError?
    @Published var isLoading = false
    
    // Configuration
    struct AppConfig {
        static let apiBaseURL = "http://localhost:3001"
        static let enableOfflineMode = true
        static let enableCaching = true
        static let enableTesting = true
        static let autoSyncInterval: TimeInterval = 300 // 5 minutes
    }
    
    enum AppError: Error, LocalizedError {
        case initializationFailed(String)
        case networkUnavailable
        case authenticationRequired
        case serviceUnavailable(String)
        
        var errorDescription: String? {
            switch self {
            case .initializationFailed(let message):
                return "应用初始化失败: \(message)"
            case .networkUnavailable:
                return "网络连接不可用"
            case .authenticationRequired:
                return "需要重新登录"
            case .serviceUnavailable(let service):
                return "\(service)服务暂时不可用"
            }
        }
    }
    
    private init() {
        setupServiceObservers()
    }
    
    // MARK: - App Initialization
    func initializeApp() {
        guard !isInitialized else { return }
        
        isLoading = true
        print("🚀 Initializing FurryKids App...")
        
        // 1. 验证网络连接
        checkNetworkConnectivity()
            .flatMap { _ in
                // 2. 初始化缓存服务
                self.initializeCacheService()
            }
            .flatMap { _ in
                // 3. 检查认证状态
                self.checkAuthenticationStatus()
            }
            .flatMap { _ in
                // 4. 同步数据
                self.performInitialDataSync()
            }
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    switch completion {
                    case .finished:
                        self?.isInitialized = true
                        self?.setupAutoSync()
                        print("✅ App initialization completed successfully")
                    case .failure(let error):
                        self?.currentError = .initializationFailed(error.localizedDescription)
                        print("❌ App initialization failed: \(error)")
                    }
                },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Service Initialization Steps
    private func checkNetworkConnectivity() -> AnyPublisher<Void, AppError> {
        return testingService.quickConnectivityTest()
            .map { isConnected in
                if !isConnected {
                    throw AppError.networkUnavailable
                }
                return ()
            }
            .setFailureType(to: AppError.self)
            .eraseToAnyPublisher()
    }
    
    private func initializeCacheService() -> AnyPublisher<Void, AppError> {
        return Just(())
            .tryMap { _ in
                // 检查缓存状态
                let cacheStats = self.cacheService.getCacheStatistics()
                print("📦 Cache initialized: \(cacheStats)")
                return ()
            }
            .mapError { _ in AppError.serviceUnavailable("Cache") }
            .eraseToAnyPublisher()
    }
    
    private func checkAuthenticationStatus() -> AnyPublisher<Void, AppError> {
        // 如果有存储的token，验证其有效性
        guard APIConfig.authToken != nil else {
            print("🔐 No stored authentication token")
            return Just(()).setFailureType(to: AppError.self).eraseToAnyPublisher()
        }
        
        return authService.getCurrentUser()
            .map { _ in
                print("🔐 Authentication verified")
                return ()
            }
            .catch { _ in
                // Token无效，清除认证信息
                self.authService.logoutLocally()
                return Just(())
            }
            .setFailureType(to: AppError.self)
            .eraseToAnyPublisher()
    }
    
    private func performInitialDataSync() -> AnyPublisher<Void, AppError> {
        guard authService.isAuthenticated else {
            print("⏭️ Skipping data sync - user not authenticated")
            return Just(()).setFailureType(to: AppError.self).eraseToAnyPublisher()
        }
        
        return dataSyncService.syncAllData()
            .map { success in
                print(success ? "📊 Initial data sync completed" : "⚠️ Initial data sync had issues")
                return ()
            }
            .setFailureType(to: AppError.self)
            .eraseToAnyPublisher()
    }
    
    // MARK: - Service Observers
    private func setupServiceObservers() {
        // 网络状态监控
        networkManager.$isConnected
            .sink { [weak self] isConnected in
                self?.isOnline = isConnected
                if isConnected {
                    self?.handleNetworkReconnection()
                } else {
                    self?.handleNetworkDisconnection()
                }
            }
            .store(in: &cancellables)
        
        // 认证状态监控
        authService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if !isAuthenticated {
                    self?.handleUserLogout()
                }
            }
            .store(in: &cancellables)
        
        // 错误监控
        Publishers.Merge4(
            feedService.$cachedFeeds.map { _ in () },
            petService.$userPets.map { _ in () },
            dataSyncService.$isSyncing.map { _ in () },
            imageUploadService.$isUploading.map { _ in () }
        )
        .sink { [weak self] _ in
            // 清除之前的错误（如果服务正常工作）
            if self?.currentError != nil {
                self?.currentError = nil
            }
        }
        .store(in: &cancellables)
    }
    
    // MARK: - Event Handlers
    private func handleNetworkReconnection() {
        print("🌐 Network reconnected")
        
        // 重新同步数据
        if authService.isAuthenticated {
            dataSyncService.syncPendingOperations()
                .sink { _ in }
                .store(in: &cancellables)
        }
    }
    
    private func handleNetworkDisconnection() {
        print("📱 Network disconnected - enabling offline mode")
        dataSyncService.enableOfflineMode()
    }
    
    private func handleUserLogout() {
        print("👋 User logged out - clearing data")
        
        // 清除缓存的用户数据
        cacheService.remove(forKey: CacheService.CacheKeys.currentUser)
        cacheService.remove(forKey: CacheService.CacheKeys.userPets)
        
        // 清除服务状态
        petService.clearCache()
        feedService.clearCache()
        dataSyncService.clearPendingOperations()
    }
    
    // MARK: - Auto Sync
    private func setupAutoSync() {
        guard AppConfig.enableOfflineMode else { return }
        
        Timer.publish(every: AppConfig.autoSyncInterval, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.performBackgroundSync()
            }
            .store(in: &cancellables)
    }
    
    private func performBackgroundSync() {
        guard authService.isAuthenticated && networkManager.isConnected else { return }
        
        dataSyncService.syncPendingOperations()
            .sink { success in
                if success {
                    print("🔄 Background sync completed")
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    func refreshAllData() {
        guard authService.isAuthenticated else { return }
        
        isLoading = true
        
        dataSyncService.syncAllData()
            .sink(
                receiveCompletion: { [weak self] _ in
                    self?.isLoading = false
                },
                receiveValue: { success in
                    print(success ? "✅ Data refresh completed" : "⚠️ Data refresh had issues")
                }
            )
            .store(in: &cancellables)
    }
    
    func clearAllData() {
        cacheService.clearAll()
        petService.clearCache()
        feedService.clearCache()
        dataSyncService.clearPendingOperations()
        authService.logoutLocally()
        
        print("🗑️ All app data cleared")
    }
    
    func runDiagnostics() {
        print("🔍 Running app diagnostics...")
        
        testingService.runAllTests()
    }
    
    func getAppStatus() -> [String: Any] {
        return [
            "isInitialized": isInitialized,
            "isOnline": isOnline,
            "isAuthenticated": authService.isAuthenticated,
            "currentUser": authService.currentUser?.username ?? "None",
            "cacheStats": cacheService.getCacheStatistics(),
            "pendingOperations": dataSyncService.getPendingOperationsCount(),
            "networkQuality": networkManager.getConnectionQuality(),
            "lastError": currentError?.localizedDescription ?? "None"
        ]
    }
    
    // MARK: - Development Helpers
    #if DEBUG
    func enableTestMode() {
        print("🧪 Test mode enabled")
        
        // 使用测试配置
        APIConfig.authToken = nil
        
        // 清除所有数据
        clearAllData()
        
        // 运行测试
        testingService.runAllTests()
    }
    
    func simulateOfflineMode() {
        print("📱 Simulating offline mode")
        dataSyncService.enableOfflineMode()
    }
    
    func simulateNetworkError() {
        print("⚠️ Simulating network error")
        currentError = .networkUnavailable
    }
    #endif
}

// MARK: - SwiftUI Environment
struct AppServiceManagerKey: EnvironmentKey {
    static let defaultValue = AppServiceManager.shared
}

extension EnvironmentValues {
    var appServiceManager: AppServiceManager {
        get { self[AppServiceManagerKey.self] }
        set { self[AppServiceManagerKey.self] = newValue }
    }
}
