import Foundation
import Combine

class AuthService: ObservableObject {
    static let shared = AuthService()

    private let networkManager = NetworkManager.shared
    private var cancellables = Set<AnyCancellable>()

    @Published var isAuthenticated = false
    @Published var currentUser: User?

    private init() {
        // 检查本地存储的token
        checkAuthenticationStatus()
    }

    // MARK: - Authentication Status
    private func checkAuthenticationStatus() {
        if let token = APIConfig.authToken, !token.isEmpty {
            // 验证token是否有效
            getCurrentUser()
                .sink(
                    receiveCompletion: { completion in
                        if case .failure = completion {
                            self.logout()
                        }
                    },
                    receiveValue: { user in
                        self.currentUser = user.user
                        self.isAuthenticated = true
                    }
                )
                .store(in: &cancellables)
        }
    }
    
    // MARK: - 用户注册
    func register(username: String, password: String, email: String? = nil) -> AnyPublisher<AuthResponse, Error> {
        let request = RegisterRequest(username: username, password: password, email: email)

        guard let requestData = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }

        return networkManager.request(
            endpoint: "/auth/register",
            method: .POST,
            body: requestData,
            responseType: AuthResponse.self
        )
        .handleEvents(receiveOutput: { [weak self] response in
            if response.success, let user = response.user, let token = response.token {
                // 保存token和用户信息
                APIConfig.authToken = token
                self?.currentUser = user
                self?.isAuthenticated = true
            }
        })
        .mapError { $0 as Error }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
    
    // MARK: - 用户登录
    func login(username: String, password: String) -> AnyPublisher<AuthResponse, Error> {
        let request = LoginRequest(username: username, password: password)

        guard let requestData = try? JSONEncoder().encode(request) else {
            return Fail(error: APIError.decodingError)
                .eraseToAnyPublisher()
        }

        return networkManager.request(
            endpoint: "/auth/login",
            method: .POST,
            body: requestData,
            responseType: AuthResponse.self
        )
        .handleEvents(receiveOutput: { [weak self] response in
            print("🔐 登录响应: success=\(response.success)")
            if let user = response.user {
                print("👤 用户信息: \(user.username)")
            }
            if let token = response.token {
                print("🎫 收到token: \(token.prefix(20))...")
            }

            if response.success, let user = response.user, let token = response.token {
                // 保存token和用户信息
                APIConfig.authToken = token
                self?.currentUser = user
                self?.isAuthenticated = true
                print("✅ 认证信息已保存")
            } else {
                print("❌ 登录失败或缺少必要信息")
            }
        })
        .mapError { $0 as Error }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
    
    // MARK: - 用户登出
    func logout() -> AnyPublisher<AuthResponse, Error> {
        return networkManager.request(
            endpoint: "/auth/logout",
            method: .POST,
            responseType: AuthResponse.self
        )
        .handleEvents(receiveOutput: { [weak self] _ in
            // 清除本地认证信息
            self?.clearAuthenticationData()
        })
        .mapError { $0 as Error }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }

    // MARK: - 本地登出
    func logoutLocally() {
        clearAuthenticationData()
    }

    private func clearAuthenticationData() {
        APIConfig.authToken = nil
        currentUser = nil
        isAuthenticated = false
    }
    
    // MARK: - 获取当前用户信息
    func getCurrentUser() -> AnyPublisher<UserInfoResponse, Error> {
        return networkManager.request(
            endpoint: "/auth/user",
            method: .GET,
            responseType: UserInfoResponse.self
        )
        .mapError { $0 as Error }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }

    // MARK: - 健康检查
    func healthCheck() -> AnyPublisher<[String: String], Error> {
        return networkManager.request(
            endpoint: "/api/health",
            method: .GET,
            responseType: [String: String].self
        )
        .mapError { $0 as Error }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }

    // MARK: - Token刷新
    func refreshToken() -> AnyPublisher<AuthResponse, Error> {
        return networkManager.request(
            endpoint: "/auth/refresh",
            method: .POST,
            responseType: AuthResponse.self
        )
        .handleEvents(receiveOutput: { [weak self] response in
            if response.success, let token = response.token {
                APIConfig.authToken = token
            }
        })
        .mapError { $0 as Error }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
} 