import SwiftUI

struct ProfileMeView: View {
    @EnvironmentObject var authStore: AuthStore
    @State private var showingLoginSheet = false
    @State private var showingRegisterSheet = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if authStore.isLoggedIn {
                    // 已登录状态
                    loggedInView
                } else {
                    // 未登录状态
                    notLoggedInView
                }
            }
            .navigationTitle("我的")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingLoginSheet) {
            LoginView()
                .environmentObject(authStore)
        }
        .sheet(isPresented: $showingRegisterSheet) {
            RegisterView()
                .environmentObject(authStore)
        }
    }
    
    // MARK: - 已登录视图
    private var loggedInView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 用户信息卡片
                userInfoCard
                
                // 功能菜单
                menuSection
                
                // 设置选项
                settingsSection
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, 16)
            .padding(.top, 20)
        }
    }
    
    // MARK: - 未登录视图
    private var notLoggedInView: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // Logo或图标
            Image(systemName: "person.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(Color(hex: "5c7d8a"))
            
            VStack(spacing: 12) {
                Text("欢迎来到毛孩子AI")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(Color(hex: "101618"))
                
                Text("登录后可以创建专属宠物档案\n与AI宠物进行个性化聊天")
                    .font(.body)
                    .foregroundColor(Color(hex: "5c7d8a"))
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
            }
            
            VStack(spacing: 16) {
                // 登录按钮
                Button(action: {
                    showingLoginSheet = true
                }) {
                    Text("登录")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color(hex: "101618"))
                        .cornerRadius(25)
                }
                
                // 注册按钮
                Button(action: {
                    showingRegisterSheet = true
                }) {
                    Text("注册新账号")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color(hex: "eaeff1"))
                        .cornerRadius(25)
                }
            }
            .padding(.horizontal, 40)
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 用户信息卡片
    private var userInfoCard: some View {
        VStack(spacing: 16) {
            HStack {
                // 头像
                AsyncImage(url: URL(string: authStore.currentUser?.avatar ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                .frame(width: 60, height: 60)
                .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(authStore.currentUser?.username ?? "用户")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(Color(hex: "101618"))
                    
                    Text("ID: \(authStore.currentUser?.id ?? 0)")
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                
                Spacer()
                
                Button(action: {
                    // 编辑资料
                }) {
                    Image(systemName: "pencil")
                        .font(.system(size: 16))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
            }
            
            // 统计信息
            HStack(spacing: 20) {
                statItem(title: "宠物", value: "\(authStore.currentUser?.petCount ?? 0)")
                statItem(title: "动态", value: "\(authStore.currentUser?.feedCount ?? 0)")
                statItem(title: "聊天", value: "\(authStore.currentUser?.chatCount ?? 0)")
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    private func statItem(title: String, value: String) -> some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(Color(hex: "101618"))
            
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(Color(hex: "5c7d8a"))
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 功能菜单
    private var menuSection: some View {
        VStack(spacing: 0) {
            menuItem(icon: "heart.fill", title: "我的收藏", action: {})
            menuItem(icon: "clock.fill", title: "浏览历史", action: {})
            menuItem(icon: "chart.bar.fill", title: "数据统计", action: {})
            menuItem(icon: "questionmark.circle.fill", title: "帮助中心", action: {})
        }
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 设置选项
    private var settingsSection: some View {
        VStack(spacing: 0) {
            menuItem(icon: "gear.fill", title: "设置", action: {})
            menuItem(icon: "info.circle.fill", title: "关于我们", action: {})
            menuItem(icon: "rectangle.portrait.and.arrow.right.fill", title: "退出登录", action: {
                authStore.logout()
            }, isLast: true)
        }
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    private func menuItem(icon: String, title: String, action: @escaping () -> Void, isLast: Bool = false) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(Color(hex: "5c7d8a"))
                    .frame(width: 20)
                
                Text(title)
                    .font(.system(size: 16))
                    .foregroundColor(Color(hex: "101618"))
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12))
                    .foregroundColor(Color(hex: "5c7d8a"))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
        }
        .background(Color.white)
        .overlay(
            Rectangle()
                .frame(height: isLast ? 0 : 0.5)
                .foregroundColor(Color(hex: "eaeff1"))
                .padding(.leading, 48),
            alignment: .bottom
        )
    }
}

#Preview {
    ProfileMeView()
        .environmentObject(AuthStore())
}
