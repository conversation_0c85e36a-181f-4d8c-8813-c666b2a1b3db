import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var petStore: PetStore
    @EnvironmentObject var authService: AuthService
    @State private var showingAddPet = false
    @State private var showingPetDetail = false
    @State private var selectedPet: Pet?
    @State private var showingDeleteAlert = false
    @State private var petToDelete: Pet?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 用户信息卡片
                    userInfoSection

                    // 宠物管理区域
                    petManagementSection

                    // 设置菜单
                    settingsSection

                    Spacer(minLength: 100)
                }
            }
            .background(Color(hex: "F9FAFB"))
            .navigationTitle("宠物档案")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                petStore.loadUserPets()
            }
        }
        .sheet(isPresented: $showingAddPet) {
            SimpleAddPetView()
                .environmentObject(petStore)
        }
        .sheet(isPresented: $showingPetDetail) {
            if let pet = selectedPet {
                SimplePetDetailView(pet: pet)
                    .environmentObject(petStore)
            }
        }
        .alert("删除宠物", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let pet = petToDelete {
                    petStore.deletePet(pet.id)
                }
            }
        } message: {
            Text("确定要删除宠物「\(petToDelete?.name ?? "")」吗？此操作无法撤销。")
        }
        .onAppear {
            petStore.loadUserPets()
        }
    }

    // MARK: - 用户信息区域
    private var userInfoSection: some View {
        VStack(spacing: 16) {
            // 头像
            AsyncImage(url: URL(string: authService.currentUser?.avatar ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(Color(hex: "5c7d8a"))
            }
            .frame(width: 80, height: 80)
            .clipShape(Circle())

            // 用户名
            Text(authService.currentUser?.username ?? "用户")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(Color(hex: "101618"))

            // 简介
            Text("宠物主人")
                .font(.system(size: 16))
                .foregroundColor(Color(hex: "5c7d8a"))

            // 统计数据
            HStack(spacing: 40) {
                statItem(title: "宠物", value: "\(petStore.pets.count)")
                statItem(title: "动态", value: "0") // TODO: 从后端获取
                statItem(title: "聊天", value: "0") // TODO: 从后端获取
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 24)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        .padding(.horizontal, 16)
        .padding(.top, 16)
    }

    // 统计项组件
    private func statItem(title: String, value: String) -> some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(Color(hex: "101618"))

            Text(title)
                .font(.system(size: 14))
                .foregroundColor(Color(hex: "5c7d8a"))
        }
    }


    // MARK: - 宠物管理区域
    private var petManagementSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题和添加按钮
            HStack {
                Text("我的宠物")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(Color(hex: "101618"))

                Spacer()

                Button(action: {
                    showingAddPet = true
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "plus")
                            .font(.system(size: 14, weight: .medium))
                        Text("添加宠物")
                            .font(.system(size: 14, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color(hex: "5c7d8a"))
                    .cornerRadius(20)
                }
            }
            .padding(.horizontal, 16)

            // 宠物列表
            if petStore.pets.isEmpty {
                emptyPetState
            } else {
                petGridView
            }
        }
        .padding(.top, 24)
    }

    // 空状态视图
    private var emptyPetState: some View {
        VStack(spacing: 16) {
            Image(systemName: "pawprint.circle")
                .font(.system(size: 60))
                .foregroundColor(Color(hex: "5c7d8a").opacity(0.6))

            VStack(spacing: 8) {
                Text("还没有宠物档案")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(Color(hex: "101618"))

                Text("添加您的第一个毛孩子，开始记录美好时光")
                    .font(.system(size: 14))
                    .foregroundColor(Color(hex: "5c7d8a"))
                    .multilineTextAlignment(.center)
            }

            Button(action: {
                showingAddPet = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus")
                    Text("添加宠物")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color(hex: "5c7d8a"))
                .cornerRadius(25)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        .padding(.horizontal, 16)
    }

    // 宠物网格视图
    private var petGridView: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 12),
            GridItem(.flexible(), spacing: 12)
        ], spacing: 16) {
            ForEach(petStore.pets) { pet in
                PetCardView(
                    pet: pet,
                    isSelected: pet.id == petStore.currentPet?.id,
                    onTap: {
                        petStore.setCurrentPet(pet)
                        selectedPet = pet
                        showingPetDetail = true
                    },
                    onEdit: {
                        selectedPet = pet
                        showingPetDetail = true
                    },
                    onDelete: {
                        petToDelete = pet
                        showingDeleteAlert = true
                    }
                )
            }
        }
        .padding(.horizontal, 16)
    }

    // MARK: - 设置区域
    private var settingsSection: some View {
        VStack(spacing: 0) {
            ProfileMenuItemView(icon: "gear", title: "设置") {
                // TODO: 打开设置页面
            }
            ProfileMenuItemView(icon: "questionmark.circle", title: "帮助与反馈") {
                // TODO: 打开帮助页面
            }
            ProfileMenuItemView(icon: "info.circle", title: "关于我们") {
                // TODO: 打开关于页面
            }
        }
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        .padding(.horizontal, 16)
        .padding(.top, 24)
    }
}

// MARK: - 宠物卡片视图
struct PetCardView: View {
    let pet: Pet
    let isSelected: Bool
    let onTap: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void

    @State private var showingActionSheet = false

    var body: some View {
        VStack(spacing: 12) {
            // 宠物头像和状态
            ZStack(alignment: .topTrailing) {
                // 头像
                Text(pet.avatar ?? "🐕")
                    .font(.system(size: 40))
                    .frame(width: 80, height: 80)
                    .background(
                        LinearGradient(
                            colors: [Color(hex: "eaeff1"), Color(hex: "f5f7f8")],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(
                                isSelected ? Color(hex: "5c7d8a") : Color.clear,
                                lineWidth: 3
                            )
                    )

                // 在线状态指示器
                if pet.isActive {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 16, height: 16)
                        .overlay(
                            Circle()
                                .stroke(Color.white, lineWidth: 2)
                        )
                        .offset(x: 4, y: -4)
                }

                // 更多操作按钮
                Button(action: {
                    showingActionSheet = true
                }) {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color(hex: "5c7d8a"))
                        .frame(width: 24, height: 24)
                        .background(Color.white.opacity(0.9))
                        .clipShape(Circle())
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
                .offset(x: -4, y: 4)
            }

            // 宠物信息
            VStack(spacing: 6) {
                // 名称
                Text(pet.name)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color(hex: "101618"))
                    .lineLimit(1)

                // 品种和年龄
                if let breed = pet.breed {
                    Text(breed)
                        .font(.system(size: 12))
                        .foregroundColor(Color(hex: "5c7d8a"))
                        .lineLimit(1)
                }

                // 等级和心情
                HStack(spacing: 8) {
                    // 等级
                    HStack(spacing: 2) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 10))
                            .foregroundColor(.orange)
                        Text("Lv.\(pet.level)")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(Color(hex: "5c7d8a"))
                    }

                    // 心情
                    Text(pet.mood)
                        .font(.system(size: 10))
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(moodColor(pet.mood))
                        .cornerRadius(8)
                }
            }
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 2)
        .onTapGesture {
            onTap()
        }
        .confirmationDialog("宠物操作", isPresented: $showingActionSheet) {
            Button("查看详情") {
                onTap()
            }
            Button("编辑信息") {
                onEdit()
            }
            Button("删除宠物", role: .destructive) {
                onDelete()
            }
            Button("取消", role: .cancel) { }
        }
    }

    // 根据心情返回对应颜色
    private func moodColor(_ mood: String) -> Color {
        switch mood {
        case "开心", "快乐":
            return .green
        case "平静", "安静":
            return .blue
        case "兴奋", "活跃":
            return .orange
        case "疲惫", "困倦":
            return .gray
        default:
            return Color(hex: "5c7d8a")
        }
    }
}

// MARK: - 菜单项视图
struct ProfileMenuItemView: View {
    let icon: String
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 18))
                    .foregroundColor(Color(hex: "5c7d8a"))
                    .frame(width: 24, height: 24)

                Text(title)
                    .font(.system(size: 16))
                    .foregroundColor(Color(hex: "101618"))

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(Color(hex: "5c7d8a"))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
        }
        .background(Color.white)
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color(hex: "eaeff1")),
            alignment: .bottom
        )
    }
}

// MARK: - 简化的添加宠物视图
struct SimpleAddPetView: View {
    @EnvironmentObject var petStore: PetStore
    @Environment(\.dismiss) private var dismiss

    @State private var name = ""
    @State private var breed = ""
    @State private var selectedAvatar = "🐕"
    @State private var isLoading = false

    private let avatarOptions = ["🐕", "🐶", "🐱", "🐈", "🐰", "🐹", "🐦", "🐠"]

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 头像选择
                VStack(spacing: 16) {
                    Text("选择头像")
                        .font(.headline)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 16) {
                        ForEach(avatarOptions, id: \.self) { avatar in
                            Button(action: {
                                selectedAvatar = avatar
                            }) {
                                Text(avatar)
                                    .font(.system(size: 32))
                                    .frame(width: 60, height: 60)
                                    .background(selectedAvatar == avatar ? Color.blue.opacity(0.2) : Color.gray.opacity(0.1))
                                    .cornerRadius(12)
                            }
                        }
                    }
                }

                // 基本信息
                VStack(spacing: 16) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("宠物名称")
                            .font(.headline)
                        TextField("请输入宠物名称", text: $name)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("品种")
                            .font(.headline)
                        TextField("请输入品种（可选）", text: $breed)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                }

                Spacer()

                // 保存按钮
                Button(action: savePet) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Text("保存")
                            .font(.headline)
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(name.isEmpty ? Color.gray : Color.blue)
                .cornerRadius(12)
                .disabled(name.isEmpty || isLoading)
            }
            .padding()
            .navigationTitle("添加宠物")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }

    private func savePet() {
        guard !name.isEmpty else { return }

        isLoading = true

        print("🐾 SimpleAddPetView: 开始添加宠物 - \(name)")

        // 调用PetStore的createPet方法
        petStore.createPet(
            name: name,
            breed: breed.isEmpty ? "未知品种" : breed,
            personalityTags: []
        )

        // 添加一个短暂的延迟后关闭页面，让用户看到操作完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isLoading = false
            dismiss()
        }
    }
}

// MARK: - 简化的宠物详情视图
struct SimplePetDetailView: View {
    @EnvironmentObject var petStore: PetStore
    @Environment(\.dismiss) private var dismiss

    let pet: Pet
    @State private var showingDeleteAlert = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 宠物头像和基本信息
                    VStack(spacing: 16) {
                        Text(pet.avatar ?? "🐕")
                            .font(.system(size: 80))

                        VStack(spacing: 8) {
                            Text(pet.name)
                                .font(.title)
                                .fontWeight(.bold)

                            if let breed = pet.breed {
                                Text(breed)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }

                    // 详细信息
                    VStack(spacing: 16) {
                        InfoRow(title: "心情", value: pet.mood)
                        InfoRow(title: "等级", value: "Lv.\(pet.level)")
                        InfoRow(title: "个性签名", value: pet.signature)

                        if !pet.personality.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("性格标签")
                                    .font(.headline)

                                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                                    ForEach(pet.personality, id: \.self) { trait in
                                        Text(trait)
                                            .font(.caption)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(Color.blue.opacity(0.1))
                                            .foregroundColor(.blue)
                                            .cornerRadius(12)
                                    }
                                }
                            }
                        }
                    }

                    Spacer(minLength: 50)

                    // 删除按钮
                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        Text("删除宠物")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.red)
                            .cornerRadius(12)
                    }
                }
                .padding()
            }
            .navigationTitle("宠物详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
            .alert("确认删除", isPresented: $showingDeleteAlert) {
                Button("取消", role: .cancel) { }
                Button("删除", role: .destructive) {
                    petStore.deletePet(pet.id)
                    dismiss()
                }
            } message: {
                Text("确定要删除\(pet.name)吗？此操作无法撤销。")
            }
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.headline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.body)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

#Preview {
    ProfileView()
        .environmentObject(PetStore())
        .environmentObject(AuthService.shared)
}