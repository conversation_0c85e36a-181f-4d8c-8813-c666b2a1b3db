import SwiftUI

struct PetDetailView: View {
    @EnvironmentObject var petStore: PetStore
    @Environment(\.dismiss) private var dismiss
    
    let pet: Pet
    @State private var isEditing = false
    @State private var showingDeleteAlert = false
    
    // 编辑状态的变量
    @State private var editName: String
    @State private var editBreed: String
    @State private var editWeight: String
    @State private var editPersonality: [String]
    @State private var editSignature: String
    @State private var editMood: String
    @State private var editAvatar: String
    
    init(pet: Pet) {
        self.pet = pet
        self._editName = State(initialValue: pet.name)
        self._editBreed = State(initialValue: pet.breed ?? "")
        self._editWeight = State(initialValue: pet.weight != nil ? String(pet.weight!) : "")
        self._editPersonality = State(initialValue: pet.personality)
        self._editSignature = State(initialValue: pet.signature)
        self._editMood = State(initialValue: pet.mood)
        self._editAvatar = State(initialValue: pet.avatar ?? "🐕")
    }
    
    // 头像选项
    private let avatarOptions = [
        "🐕", "🐶", "🦮", "🐕‍🦺", "🐩",
        "🐱", "🐈", "🐈‍⬛", "🦁", "🐯",
        "🐰", "🐹", "🐭", "🐦", "🐧",
        "🐠", "🐟", "🐡", "🦈", "🐙"
    ]
    
    // 性格标签
    private let personalityOptions = [
        "活泼", "安静", "友善", "独立", "粘人",
        "聪明", "调皮", "温顺", "勇敢", "害羞",
        "好奇", "懒惰", "忠诚", "机警", "温柔"
    ]
    
    // 心情选项
    private let moodOptions = ["开心", "平静", "兴奋", "困倦", "好奇"]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 宠物头像和基本信息
                    petHeaderSection
                    
                    // 详细信息卡片
                    if isEditing {
                        editingSection
                    } else {
                        detailSection
                    }
                    
                    // 统计信息
                    statsSection
                    
                    // 操作按钮
                    if !isEditing {
                        actionButtonsSection
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 16)
                .padding(.bottom, 100)
            }
            .background(Color(hex: "F9FAFB"))
            .navigationTitle(isEditing ? "编辑宠物" : pet.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(isEditing ? "取消" : "关闭") {
                        if isEditing {
                            cancelEditing()
                        } else {
                            dismiss()
                        }
                    }
                }
                
                if isEditing {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("保存") {
                            saveChanges()
                        }
                        .disabled(editName.isEmpty || editBreed.isEmpty)
                    }
                }
            }
        }
        .alert("删除宠物", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                petStore.deletePet(pet.id)
                dismiss()
            }
        } message: {
            Text("确定要删除宠物「\(pet.name)」吗？此操作无法撤销。")
        }
    }
    
    // MARK: - 宠物头像和基本信息
    private var petHeaderSection: some View {
        VStack(spacing: 20) {
            // 头像
            if isEditing {
                VStack(spacing: 16) {
                    Text(editAvatar)
                        .font(.system(size: 80))
                        .frame(width: 120, height: 120)
                        .background(
                            LinearGradient(
                                colors: [Color(hex: "eaeff1"), Color(hex: "f5f7f8")],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color(hex: "5c7d8a"), lineWidth: 3)
                        )
                    
                    // 头像选择
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 5), spacing: 8) {
                        ForEach(avatarOptions, id: \.self) { avatar in
                            Button(action: {
                                editAvatar = avatar
                            }) {
                                Text(avatar)
                                    .font(.system(size: 20))
                                    .frame(width: 40, height: 40)
                                    .background(
                                        editAvatar == avatar ? 
                                        Color(hex: "5c7d8a").opacity(0.2) : 
                                        Color(hex: "eaeff1").opacity(0.5)
                                    )
                                    .clipShape(Circle())
                                    .overlay(
                                        Circle()
                                            .stroke(
                                                editAvatar == avatar ? 
                                                Color(hex: "5c7d8a") : Color.clear,
                                                lineWidth: 2
                                            )
                                    )
                            }
                        }
                    }
                }
            } else {
                ZStack(alignment: .bottomTrailing) {
                    Text(pet.avatar ?? "🐕")
                        .font(.system(size: 80))
                        .frame(width: 120, height: 120)
                        .background(
                            LinearGradient(
                                colors: [Color(hex: "eaeff1"), Color(hex: "f5f7f8")],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color(hex: "5c7d8a"), lineWidth: 3)
                        )
                    
                    // 在线状态
                    if pet.isActive {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 24, height: 24)
                            .overlay(
                                Circle()
                                    .stroke(Color.white, lineWidth: 3)
                            )
                            .offset(x: 8, y: 8)
                    }
                }
            }
            
            // 基本信息
            VStack(spacing: 8) {
                Text(isEditing ? editName : pet.name)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(Color(hex: "101618"))
                
                Text(isEditing ? editBreed : (pet.breed ?? "未知品种"))
                    .font(.system(size: 16))
                    .foregroundColor(Color(hex: "5c7d8a"))
                
                // 等级和经验
                HStack(spacing: 16) {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 14))
                            .foregroundColor(.orange)
                        Text("Lv.\(pet.level)")
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(Color(hex: "101618"))
                    }
                    
                    Text("经验: \(pet.experience)/\(pet.maxExperience)")
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "5c7d8a"))
                }
                
                // 经验进度条
                ProgressView(value: Double(pet.experience), total: Double(pet.maxExperience))
                    .progressViewStyle(LinearProgressViewStyle(tint: .orange))
                    .frame(height: 6)
                    .scaleEffect(x: 1, y: 1.5, anchor: .center)
                    .clipShape(Capsule())
                    .frame(maxWidth: 200)
            }
        }
        .padding(24)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.08), radius: 12, x: 0, y: 4)
    }
    
    // MARK: - 详细信息展示
    private var detailSection: some View {
        VStack(spacing: 16) {
            // 性格标签
            if !pet.personality.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("性格特点")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color(hex: "101618"))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                        ForEach(pet.personality, id: \.self) { trait in
                            Text(trait)
                                .font(.system(size: 12, weight: .medium))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color(hex: "5c7d8a").opacity(0.1))
                                .foregroundColor(Color(hex: "5c7d8a"))
                                .cornerRadius(12)
                        }
                    }
                }
            }
            
            // 个性签名
            if !pet.signature.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("个性签名")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color(hex: "101618"))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    Text(pet.signature)
                        .font(.system(size: 14))
                        .foregroundColor(Color(hex: "5c7d8a"))
                        .padding(12)
                        .background(Color(hex: "F9FAFB"))
                        .cornerRadius(8)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            
            // 其他信息
            VStack(spacing: 12) {
                infoRow(title: "当前心情", value: pet.mood, color: moodColor(pet.mood))
                
                if let weight = pet.weight {
                    infoRow(title: "体重", value: "\(weight, specifier: "%.1f") kg")
                }
                
                if let healthStatus = pet.healthStatus {
                    infoRow(title: "健康状态", value: healthStatus)
                }
                
                infoRow(title: "状态", value: pet.status)
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 编辑模式
    private var editingSection: some View {
        VStack(spacing: 20) {
            // 基本信息编辑
            VStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("宠物名称 *")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    TextField("请输入宠物名称", text: $editName)
                        .textFieldStyle(CustomTextFieldStyle())
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("品种 *")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    TextField("请输入品种", text: $editBreed)
                        .textFieldStyle(CustomTextFieldStyle())
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("体重 (kg)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    TextField("请输入体重", text: $editWeight)
                        .keyboardType(.decimalPad)
                        .textFieldStyle(CustomTextFieldStyle())
                }
            }
            
            // 性格标签编辑
            VStack(alignment: .leading, spacing: 12) {
                Text("性格标签")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color(hex: "101618"))
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                    ForEach(personalityOptions, id: \.self) { personality in
                        Button(action: {
                            togglePersonality(personality)
                        }) {
                            Text(personality)
                                .font(.system(size: 12, weight: .medium))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(
                                    editPersonality.contains(personality) ? 
                                    Color(hex: "5c7d8a") : 
                                    Color(hex: "eaeff1")
                                )
                                .foregroundColor(
                                    editPersonality.contains(personality) ? 
                                    .white : 
                                    Color(hex: "101618")
                                )
                                .cornerRadius(16)
                        }
                        .disabled(editPersonality.count >= 5 && !editPersonality.contains(personality))
                    }
                }
            }
            
            // 其他信息编辑
            VStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("个性签名")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    TextField("为您的宠物写一句话...", text: $editSignature, axis: .vertical)
                        .lineLimit(3)
                        .textFieldStyle(CustomTextFieldStyle())
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("当前心情")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "101618"))
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(moodOptions, id: \.self) { mood in
                                Button(action: {
                                    editMood = mood
                                }) {
                                    Text(mood)
                                        .font(.system(size: 14, weight: .medium))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            editMood == mood ? 
                                            Color(hex: "5c7d8a") : 
                                            Color(hex: "eaeff1")
                                        )
                                        .foregroundColor(
                                            editMood == mood ? 
                                            .white : 
                                            Color(hex: "101618")
                                        )
                                        .cornerRadius(20)
                                }
                            }
                        }
                        .padding(.horizontal, 2)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 统计信息
    private var statsSection: some View {
        VStack(spacing: 16) {
            Text("活动统计")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(Color(hex: "101618"))
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 16) {
                statCard(title: "动态", value: "0", icon: "photo")
                statCard(title: "聊天", value: "0", icon: "message")
                statCard(title: "互动", value: "0", icon: "heart")
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
    
    // MARK: - 操作按钮
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            Button(action: {
                isEditing = true
            }) {
                HStack {
                    Image(systemName: "pencil")
                    Text("编辑信息")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color(hex: "5c7d8a"))
                .cornerRadius(12)
            }
            
            Button(action: {
                showingDeleteAlert = true
            }) {
                HStack {
                    Image(systemName: "trash")
                    Text("删除宠物")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.red)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color.red.opacity(0.1))
                .cornerRadius(12)
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 辅助组件和方法
    private func infoRow(title: String, value: String, color: Color? = nil) -> some View {
        HStack {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color(hex: "101618"))
            
            Spacer()
            
            Text(value)
                .font(.system(size: 14))
                .foregroundColor(color ?? Color(hex: "5c7d8a"))
        }
        .padding(.vertical, 4)
    }
    
    private func statCard(title: String, value: String, icon: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(Color(hex: "5c7d8a"))
            
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(Color(hex: "101618"))
            
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(Color(hex: "5c7d8a"))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(hex: "F9FAFB"))
        .cornerRadius(8)
    }
    
    private func moodColor(_ mood: String) -> Color {
        switch mood {
        case "开心", "快乐":
            return .green
        case "平静", "安静":
            return .blue
        case "兴奋", "活跃":
            return .orange
        case "疲惫", "困倦":
            return .gray
        default:
            return Color(hex: "5c7d8a")
        }
    }
    
    private func togglePersonality(_ personality: String) {
        if editPersonality.contains(personality) {
            editPersonality.removeAll { $0 == personality }
        } else if editPersonality.count < 5 {
            editPersonality.append(personality)
        }
    }
    
    private func cancelEditing() {
        // 重置编辑状态
        editName = pet.name
        editBreed = pet.breed ?? ""
        editWeight = pet.weight != nil ? String(pet.weight!) : ""
        editPersonality = pet.personality
        editSignature = pet.signature
        editMood = pet.mood
        editAvatar = pet.avatar ?? "🐕"
        isEditing = false
    }
    
    private func saveChanges() {
        let weightValue = Double(editWeight)
        
        petStore.updatePet(
            pet.id,
            name: editName,
            breed: editBreed,
            weight: weightValue,
            personalityTags: editPersonality
        )
        
        isEditing = false
    }
}

#Preview {
    PetDetailView(pet: Pet.sample)
        .environmentObject(PetStore())
}
