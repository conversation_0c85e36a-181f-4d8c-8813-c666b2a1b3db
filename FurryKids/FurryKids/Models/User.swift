import Foundation

// MARK: - 用户数据模型
struct User: Codable, Identifiable {
    let id: Int
    let username: String
    let provider: String // "local", "google", "facebook"
    let email: String?
    let displayName: String?
    let avatar: String?
    let petCount: Int?
    let feedCount: Int?
    let chatCount: Int?
    let createdAt: String?
    let updatedAt: String?

    var isLoggedIn: Bool {
        return id > 0
    }
}

// MARK: - 认证请求模型
struct LoginRequest: Codable {
    let username: String
    let password: String
}

struct RegisterRequest: Codable {
    let username: String
    let password: String
    let email: String?
}

// MARK: - 认证响应模型
struct AuthResponse: Codable {
    let success: Bool
    let message: String
    let user: User?
    let token: String?  // JWT token
    let refreshToken: String?  // 刷新token

    enum CodingKeys: String, CodingKey {
        case success, message, user, refreshToken
        case token = "access_token"  // 映射后端的access_token字段
    }
}

struct UserInfoResponse: Codable {
    let user: User?
    let authType: String?
}

struct ErrorResponse: Codable {
    let success: Bool
    let message: String
    let code: String?
} 