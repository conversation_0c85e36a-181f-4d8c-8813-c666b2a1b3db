import Foundation

// MARK: - Pet相关枚举定义

enum PetGender: String, Codable, CaseIterable {
    case male = "male"
    case female = "female"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .male: return "公"
        case .female: return "母"
        case .unknown: return "未知"
        }
    }
}

enum PetSize: String, Codable, CaseIterable {
    case tiny = "tiny"
    case small = "small"
    case medium = "medium"
    case large = "large"
    case giant = "giant"
    
    var displayName: String {
        switch self {
        case .tiny: return "迷你"
        case .small: return "小型"
        case .medium: return "中型"
        case .large: return "大型"
        case .giant: return "巨型"
        }
    }
}

enum PetMood: String, Codable, CaseIterable {
    case happy = "happy"
    case excited = "excited"
    case calm = "calm"
    case sleepy = "sleepy"
    case playful = "playful"
    case hungry = "hungry"
    case sad = "sad"
    case anxious = "anxious"
    case angry = "angry"
    case confused = "confused"
    case content = "content"
    case energetic = "energetic"
    
    var displayName: String {
        switch self {
        case .happy: return "开心"
        case .excited: return "兴奋"
        case .calm: return "平静"
        case .sleepy: return "困倦"
        case .playful: return "顽皮"
        case .hungry: return "饥饿"
        case .sad: return "伤心"
        case .anxious: return "焦虑"
        case .angry: return "生气"
        case .confused: return "困惑"
        case .content: return "满足"
        case .energetic: return "精力充沛"
        }
    }
    
    var emoji: String {
        switch self {
        case .happy: return "😊"
        case .excited: return "🤩"
        case .calm: return "😌"
        case .sleepy: return "😴"
        case .playful: return "😜"
        case .hungry: return "🤤"
        case .sad: return "😢"
        case .anxious: return "😰"
        case .angry: return "😠"
        case .confused: return "😕"
        case .content: return "😌"
        case .energetic: return "⚡"
        }
    }
}

enum PetType: String, Codable, CaseIterable {
    case dog = "dog"
    case cat = "cat"
    case rabbit = "rabbit"
    case bird = "bird"
    case fish = "fish"
    case hamster = "hamster"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .dog: return "狗"
        case .cat: return "猫"
        case .rabbit: return "兔子"
        case .bird: return "鸟"
        case .fish: return "鱼"
        case .hamster: return "仓鼠"
        case .other: return "其他"
        }
    }
    
    var emoji: String {
        switch self {
        case .dog: return "🐶"
        case .cat: return "🐱"
        case .rabbit: return "🐰"
        case .bird: return "🐦"
        case .fish: return "🐠"
        case .hamster: return "🐹"
        case .other: return "🐾"
        }
    }
}

enum PetHealthStatus: String, Codable, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    case sick = "sick"
    case recovering = "recovering"
    
    var displayName: String {
        switch self {
        case .excellent: return "极佳"
        case .good: return "良好"
        case .fair: return "一般"
        case .poor: return "较差"
        case .sick: return "生病"
        case .recovering: return "康复中"
        }
    }
    
    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .fair: return "yellow"
        case .poor: return "orange"
        case .sick: return "red"
        case .recovering: return "purple"
        }
    }
}
