import Foundation

// MARK: - Message数据模型 (匹配backend API)
enum MessageType: String, Codable {
    case user = "user"
    case pet = "pet"
}

struct Message: Codable, Identifiable {
    let id: Int
    let conversationId: Int
    let content: String
    let type: MessageType
    let mood: String?
    let actions: [String]?
    let emotions: [String]?
    let confidence: Double?
    let tokensUsed: Int?
    let createdAt: Date
    let isRead: Bool

    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, content, type, mood, actions, emotions, confidence, createdAt
        case conversationId = "conversation_id"
        case tokensUsed = "tokens_used"
        case isRead = "is_read"
    }
}

// MARK: - Conversation数据模型
struct Conversation: Codable, Identifiable {
    let id: Int
    let userId: Int
    let petId: Int
    let isActive: Bool
    let createdAt: Date
    let updatedAt: Date

    // 关联数据
    let pet: Pet?
    let lastMessage: Message?
    let messageCount: Int

    enum CodingKeys: String, CodingKey {
        case id, isActive, createdAt, updatedAt, pet, messageCount
        case userId = "user_id"
        case petId = "pet_id"
        case lastMessage = "last_message"
    }
}

// MARK: - AI聊天请求/响应模型
struct ChatRequest: Codable {
    let message: String
    let petId: Int
    let contextData: [String: String]?

    enum CodingKeys: String, CodingKey {
        case message
        case petId = "pet_id"
        case contextData = "context_data"
    }
}

struct ChatResponse: Codable {
    let reply: String
    let mood: String
    let actions: [String]
    let emotions: [String]
    let confidence: Double
    let conversationId: Int
    let messageId: Int
    let responseTime: Double
    let tokensUsed: Int

    enum CodingKeys: String, CodingKey {
        case reply, mood, actions, emotions, confidence, responseTime
        case conversationId = "conversation_id"
        case messageId = "message_id"
        case tokensUsed = "tokens_used"
    }
}

// 示例数据
extension Message {
    static let samples = [
        Message(
            id: 1,
            conversationId: 1,
            content: "汪汪！主人你回来啦！我好想你呀~ 🐕💕",
            type: .pet,
            mood: "兴奋",
            actions: ["摇尾巴", "跳跃"],
            emotions: ["开心", "兴奋"],
            confidence: 0.92,
            tokensUsed: 45,
            createdAt: Date().addingTimeInterval(-3600),
            isRead: true
        ),
        Message(
            id: 2,
            conversationId: 1,
            content: "我也很想你！今天乖不乖？",
            type: .user,
            mood: nil,
            actions: nil,
            emotions: nil,
            confidence: nil,
            tokensUsed: nil,
            createdAt: Date().addingTimeInterval(-3500),
            isRead: true
        ),
        Message(
            id: 3,
            conversationId: 1,
            content: "我超级乖的！一直在等你回家，还自己玩了一会儿球球。不过有点想出去散步...",
            type: .pet,
            mood: "期待",
            actions: ["看向门口", "摇尾巴"],
            emotions: ["期待", "开心"],
            confidence: 0.88,
            tokensUsed: 52,
            createdAt: Date().addingTimeInterval(-3400),
            isRead: true
        )
    ]
} 