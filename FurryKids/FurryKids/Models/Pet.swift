import Foundation

// MARK: - Pet数据模型 (兼容前端使用)
struct Pet: Codable, Identifiable {
    var id: Int  // 改为Int类型以匹配后端API
    let ownerId: Int?
    var name: String
    let type: String?  // "dog", "cat", "rabbit", etc.
    let breed: String?
    var personality: [String]
    var avatar: String?
    let birthday: Date?
    let weight: Double?
    let healthStatus: String?
    var lastFeedTime: Date?
    var lastWalkTime: Date?
    let createdAt: Date?
    let updatedAt: Date?

    // 前端需要的额外属性
    var signature: String
    var mood: String
    var status: String
    var level: Int
    var experience: Int
    var maxExperience: Int

    // 前端显示用的计算属性
    var age: Int {
        guard let birthday = birthday else { return 0 }
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: birthday, to: Date())
        return ageComponents.year ?? 0
    }

    var personalityTags: [String] {
        return personality
    }

    // PetStore需要的计算属性
    var currentMood: String {
        return mood
    }

    var isActive: Bool {
        return status == "在线" || status == "active" || status == "online"
    }

    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, name, type, breed, personality, avatar, birthday, weight, createdAt, updatedAt
        case signature, mood, status, level, experience, maxExperience
        case ownerId = "owner_id"
        case healthStatus = "health_status"
        case lastFeedTime = "last_feed_time"
        case lastWalkTime = "last_walk_time"
    }
}

// MARK: - Pet创建/更新请求模型
struct PetCreateRequest: Codable {
    let name: String
    let type: String
    let breed: String?
    let personality: [String]  // 改为数组类型
    let avatar: String?
    let birthday: Date?
    let weight: Double?
    let healthStatus: String

    // 前端特有的属性
    let signature: String?
    let mood: String?
    let status: String?
    let level: Int?
    let experience: Int?
    let maxExperience: Int?
    let moodDescription: String?
    let aiPersonalityPrompt: String?

    enum CodingKeys: String, CodingKey {
        case name, type, breed, personality, avatar, birthday, weight
        case signature, mood, status, level, experience, maxExperience
        case healthStatus = "health_status"
        case moodDescription = "mood_description"
        case aiPersonalityPrompt = "ai_personality_prompt"
    }
}

struct PetUpdateRequest: Codable {
    let name: String?
    let type: String?
    let breed: String?
    let personality: [String]?  // 改为数组类型
    let avatar: String?
    let birthday: Date?
    let weight: Double?
    let healthStatus: String?

    // 前端特有的属性
    let signature: String?
    let mood: String?
    let status: String?
    let level: Int?
    let experience: Int?
    let maxExperience: Int?
    let moodDescription: String?
    let aiPersonalityPrompt: String?

    enum CodingKeys: String, CodingKey {
        case name, type, breed, personality, avatar, birthday, weight
        case signature, mood, status, level, experience, maxExperience
        case healthStatus = "health_status"
        case moodDescription = "mood_description"
        case aiPersonalityPrompt = "ai_personality_prompt"
    }
}

// 示例数据
extension Pet {
    static let sample = Pet(
        id: 1,
        ownerId: 1,
        name: "Buddy",
        type: "dog",
        breed: "柴犬",
        personality: ["活泼", "好奇", "友善"],
        avatar: "🐶",
        birthday: Calendar.current.date(byAdding: .year, value: -2, to: Date()),
        weight: 25.5,
        healthStatus: "健康",
        lastFeedTime: Date().addingTimeInterval(-3600),
        lastWalkTime: Date().addingTimeInterval(-1800),
        createdAt: Date().addingTimeInterval(-86400 * 30),
        updatedAt: Date(),
        signature: "我是一只可爱的柴犬，喜欢玩球和晒太阳！",
        mood: "开心",
        status: "在线",
        level: 5,
        experience: 75,
        maxExperience: 100
    )

    static let sampleData = [
        Pet(
            id: 2,
            ownerId: 1,
            name: "小毛球",
            type: "dog",
            breed: "金毛",
            personality: ["活泼", "粘人", "聪明"],
            avatar: "🐕",
            birthday: Calendar.current.date(byAdding: .year, value: -2, to: Date()),
            weight: 25.5,
            healthStatus: "健康",
            lastFeedTime: Date().addingTimeInterval(-3600),
            lastWalkTime: Date().addingTimeInterval(-1800),
            createdAt: Date().addingTimeInterval(-86400 * 30),
            updatedAt: Date(),
            signature: "我是一只活泼的金毛，喜欢和主人一起玩耍！",
            mood: "开心",
            status: "在线",
            level: 3,
            experience: 45,
            maxExperience: 100
        ),

        Pet(
            id: 3,
            ownerId: 1,
            name: "小橘猫",
            type: "cat",
            breed: "橘猫",
            personality: ["慵懒", "好奇", "独立"],
            avatar: "🐱",
            birthday: Calendar.current.date(byAdding: .year, value: -1, to: Date()),
            weight: 4.2,
            healthStatus: "健康",
            lastFeedTime: Date().addingTimeInterval(-7200),
            lastWalkTime: nil,
            createdAt: Date().addingTimeInterval(-86400 * 20),
            updatedAt: Date(),
            signature: "窗边的阳光是我最爱的地方~",
            mood: "慵懒",
            status: "离线",
            level: 2,
            experience: 30,
            maxExperience: 80
        ),

        Pet(
            id: 4,
            ownerId: 1,
            name: "旺财",
            type: "dog",
            breed: "德牧",
            personality: ["忠诚", "警觉", "勇敢"],
            avatar: "🐕‍🦺",
            birthday: Calendar.current.date(byAdding: .year, value: -3, to: Date()),
            weight: 35.0,
            healthStatus: "健康",
            lastFeedTime: Date().addingTimeInterval(-1800),
            lastWalkTime: Date().addingTimeInterval(-900),
            createdAt: Date().addingTimeInterval(-86400 * 60),
            updatedAt: Date(),
            signature: "保护主人是我的责任！",
            mood: "警惕",
            status: "在线",
            level: 4,
            experience: 60,
            maxExperience: 120
        )
    ]
}