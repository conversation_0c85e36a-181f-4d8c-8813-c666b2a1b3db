import Foundation

// MARK: - Feed数据模型 (匹配backend API)
struct Feed: Codable, Identifiable {
    let id: Int
    let petId: Int
    let userId: Int
    let content: String
    let images: [String]
    let tags: [String]
    let mood: String?
    let location: String?
    let visibility: String  // "public", "private", "friends"
    let status: String      // "published", "draft", "hidden", "deleted"
    var likesCount: Int
    var commentsCount: Int
    var sharesCount: Int
    let viewsCount: Int
    var isLiked: Bool
    let createdAt: Date
    let updatedAt: Date

    // 关联数据 (从API获取时可能包含)
    let pet: Pet?


    // 编码键映射
    enum CodingKeys: String, CodingKey {
        case id, content, images, tags, mood, location, visibility, status, createdAt, updatedAt, pet
        case petId = "pet_id"
        case userId = "user_id"
        case likesCount = "likes_count"
        case commentsCount = "comments_count"
        case sharesCount = "shares_count"
        case viewsCount = "views_count"
        case isLiked = "is_liked"
    }
}

// MARK: - Feed创建/更新请求模型
struct FeedCreateRequest: Codable {
    let petId: Int
    let content: String
    let images: [String]
    let tags: [String]
    let mood: String?
    let location: String?
    let visibility: String

    enum CodingKeys: String, CodingKey {
        case content, images, tags, mood, location, visibility
        case petId = "pet_id"
    }
}

struct FeedUpdateRequest: Codable {
    let content: String?
    let images: [String]?
    let tags: [String]?
    let mood: String?
    let location: String?
    let visibility: String?
    let status: String?
}

// MARK: - Feed互动模型
struct FeedLike: Codable {
    let id: Int
    let feedId: Int
    let userId: Int
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, createdAt
        case feedId = "feed_id"
        case userId = "user_id"
    }
}

struct FeedComment: Codable, Identifiable {
    let id: Int
    let feedId: Int
    let userId: Int
    let content: String
    let parentId: Int?
    let isDeleted: Bool
    let isHidden: Bool
    let createdAt: Date
    let updatedAt: Date

    // 关联数据
    let userName: String?
    let userAvatar: String?
    let isReply: Bool
    let replyCount: Int
    let replies: [FeedComment]?

    enum CodingKeys: String, CodingKey {
        case id, content, createdAt, updatedAt, replies
        case feedId = "feed_id"
        case userId = "user_id"
        case parentId = "parent_id"
        case isDeleted = "is_deleted"
        case isHidden = "is_hidden"
        case userName = "user_name"
        case userAvatar = "user_avatar"
        case isReply = "is_reply"
        case replyCount = "reply_count"
    }
}

// MARK: - Feed评论列表响应
struct FeedCommentListResponse: Codable {
    let comments: [FeedComment]
    let total: Int
    let page: Int
    let size: Int
    let hasMore: Bool

    enum CodingKeys: String, CodingKey {
        case comments, total, page, size
        case hasMore = "has_more"
    }
}

// 示例数据
extension Feed {
    static let sampleData = [
        Feed(
            id: 1,
            petId: 1,
            userId: 1,
            content: "今天和主人去公园玩了，好开心！🌳🏃‍♂️",
            images: ["park_image1"],
            tags: ["户外活动", "遛狗"],
            mood: "开心",
            location: "中央公园",
            visibility: "public",
            status: "published",
            likesCount: 42,
            commentsCount: 12,
            sharesCount: 3,
            viewsCount: 156,
            isLiked: false,
            createdAt: Date().addingTimeInterval(-3600),
            updatedAt: Date().addingTimeInterval(-3600),
            pet: Pet.sampleData.first
        ),
        Feed(
            id: 2,
            petId: 2,
            userId: 1,
            content: "窗边晒太阳真舒服，这是我的最爱！☀️😌",
            images: ["sunbath_image"],
            tags: ["晒太阳", "休闲"],
            mood: "慵懒",
            location: nil,
            visibility: "public",
            status: "published",
            likesCount: 35,
            commentsCount: 8,
            sharesCount: 1,
            viewsCount: 89,
            isLiked: true,
            createdAt: Date().addingTimeInterval(-7200),
            updatedAt: Date().addingTimeInterval(-7200),
            pet: Pet.sampleData.count > 1 ? Pet.sampleData[1] : nil
        ),
        Feed(
            id: 3,
            petId: 3,
            userId: 1,
            content: "刚刚吃了超级好吃的胡萝卜！🥕 我的最爱！",
            images: ["carrot_image"],
            tags: ["美食", "零食"],
            mood: "满足",
            location: nil,
            visibility: "public",
            status: "published",
            likesCount: 28,
            commentsCount: 5,
            sharesCount: 0,
            viewsCount: 67,
            isLiked: false,
            createdAt: Date().addingTimeInterval(-10800),
            updatedAt: Date().addingTimeInterval(-10800),
            pet: Pet.sampleData.count > 2 ? Pet.sampleData[2] : nil
        )
    ]
} 