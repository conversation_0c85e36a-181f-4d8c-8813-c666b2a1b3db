---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.5/Metal.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1746660347000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.5/Metal.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
    size:            104052
  - mtime:           1745047443000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1929404
    sdk_relative:    true
  - mtime:           1745048235000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745043435000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1745044333000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1745209361000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1745379238000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1745045809000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1743191765000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77528
    sdk_relative:    true
  - mtime:           1745048441000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3853
    sdk_relative:    true
  - mtime:           1745048462000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1064
    sdk_relative:    true
  - mtime:           1745048462000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1065
    sdk_relative:    true
  - mtime:           1745048456000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1028
    sdk_relative:    true
  - mtime:           1745048456000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1476
    sdk_relative:    true
  - mtime:           1745048468000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            816
    sdk_relative:    true
  - mtime:           1745048370000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            15247
    sdk_relative:    true
  - mtime:           1745047474000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            4224
    sdk_relative:    true
  - mtime:           1745048479000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            18218
    sdk_relative:    true
  - mtime:           1745049022000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            230593
    sdk_relative:    true
  - mtime:           1745049158000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22870
    sdk_relative:    true
  - mtime:           1745049689000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            167797
    sdk_relative:    true
  - mtime:           1745049682000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            6560
    sdk_relative:    true
  - mtime:           1745049812000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            57133
    sdk_relative:    true
  - mtime:           1745049955000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22822
    sdk_relative:    true
  - mtime:           1745049983000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            33617
    sdk_relative:    true
  - mtime:           1745049028000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3451
    sdk_relative:    true
  - mtime:           1745049689000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            95467
    sdk_relative:    true
  - mtime:           1745050377000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            991698
    sdk_relative:    true
  - mtime:           1745050643000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            25973
    sdk_relative:    true
version:         1
...
