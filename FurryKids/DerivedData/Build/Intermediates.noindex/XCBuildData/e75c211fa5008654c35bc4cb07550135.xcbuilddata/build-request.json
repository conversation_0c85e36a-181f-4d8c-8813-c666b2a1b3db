{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "e2d40c85f6dcc60effa4c5c463d3c322e0bd1a58b8667f74c5b4c67ede0d85f5"}], "containerPath": "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.5", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.5", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "78", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "AE6955CB-B5FC-4D4D-91E4-9FC74CCC5E75", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "18.5", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}