{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app": {"is-mutated": true}, "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids": {"is-mutated": true}, "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib": {"is-mutated": true}, "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/_CodeSignature", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib>", "<target-FurryKids-****************************************************************--begin-scanning>", "<target-FurryKids-****************************************************************--end>", "<target-FurryKids-****************************************************************--linker-inputs-ready>", "<target-FurryKids-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-FurryKids-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/ssu/root.ssu.yaml", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/_CodeSignature", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Assets.car", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_signature", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/PkgInfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_lto.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-non-framework-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist"], "roots": ["/tmp/FurryKids.dst", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products"], "outputs": ["<target-FurryKids-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids-e2d40c85f6dcc60effa4c5c463d3c322-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<ClangStatCache /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids.xcodeproj", "signature": "fa571bf22c0f5dd33e5526b15a44735a"}, "P0:::CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids-e2d40c85f6dcc60effa4c5c463d3c322-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-FurryKids-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Metadata.appintents>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList"], "outputs": ["<target-FurryKids-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-ChangePermissions>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-StripSymbols>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib>", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-GenerateStubAPI>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ProductPostprocessingTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-CodeSign>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-Validate>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-CopyAside>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-FurryKids-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "outputs": ["<target-FurryKids-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-FurryKids-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--GeneratedFilesTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ProductStructureTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-FurryKids-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-non-framework-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.hmap"], "outputs": ["<target-FurryKids-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/PkgInfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist"], "outputs": ["<target-FurryKids-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--RealityAssetsTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--ModuleMapTaskProducer>", "<target-FurryKids-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-FurryKids-****************************************************************--InfoPlistTaskProducer>", "<target-FurryKids-****************************************************************--SanitizerTaskProducer>", "<target-FurryKids-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-FurryKids-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-FurryKids-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-FurryKids-****************************************************************--TestTargetTaskProducer>", "<target-FurryKids-****************************************************************--TestHostTaskProducer>", "<target-FurryKids-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-FurryKids-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-FurryKids-****************************************************************--DocumentationTaskProducer>", "<target-FurryKids-****************************************************************--CustomTaskProducer>", "<target-FurryKids-****************************************************************--StubBinaryTaskProducer>", "<target-FurryKids-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--start>", "<target-FurryKids-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "outputs": ["<target-FurryKids-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--HeadermapTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "outputs": ["<target-FurryKids-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ProductPostprocessingTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-FurryKids-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-FurryKids-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Assets.car", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_signature", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_lto.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json"], "outputs": ["<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>"]}, "P0:::Gate target-FurryKids-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-FurryKids-****************************************************************--generated-headers>"]}, "P0:::Gate target-FurryKids-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h"], "outputs": ["<target-FurryKids-****************************************************************--swift-generated-headers>"]}, "P0:target-FurryKids-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Metadata.appintents>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--entry>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist", "--temp-dir-path", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/ssu", "--bundle-id", "com.example.FurryKids", "--product-path", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "--extracted-metadata-path", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Metadata.appintents", "--metadata-file-list", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "signature": "a30882fb6fd74d6d88ec325c93a5632f"}, "P0:target-FurryKids-****************************************************************-:Debug:CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/ContentView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/FurryKidsApp.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Feed.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Message.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Pet.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Stores/InteractionStore.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/Color+Extensions.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/SpeechHelper.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/CreateFeedView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/FeedView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/InteractionView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/ProfileView.swift/", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib>", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>", "<target-FurryKids-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids normal>", "<TRIGGER: MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/_CodeSignature", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<TRIGGER: CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"]}, "P0:target-FurryKids-****************************************************************-:Debug:CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/ContentView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/FurryKidsApp.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Feed.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Message.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Pet.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Stores/InteractionStore.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/Color+Extensions.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/SpeechHelper.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/CreateFeedView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/FeedView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/InteractionView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/ProfileView.swift/", "<target-FurryKids-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib>"]}, "P0:target-FurryKids-****************************************************************-:Debug:CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/ContentView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/FurryKidsApp.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Feed.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Message.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Pet.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Stores/InteractionStore.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/Color+Extensions.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/SpeechHelper.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/CreateFeedView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/FeedView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/InteractionView.swift/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/ProfileView.swift/", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib>", "<target-FurryKids-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>"]}, "P0:target-FurryKids-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets/", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets", "--compile", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone17,3", "--filter-for-device-os-version", "18.5", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "15.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "control-enabled": false, "deps": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "747c9b1a2d146396fa8cad19ac97b7b3"}, "P0:target-FurryKids-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets/", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets", "--compile", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "15.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "control-enabled": false, "deps": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "837c5a1cb30ec381c7b75d98bbbc6b1d"}, "P0:target-FurryKids-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "deps": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-FurryKids-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/ContentView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/FurryKidsApp.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Pet.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Feed.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Message.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/Color+Extensions.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/FeedView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/ProfileView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/InteractionView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/CreateFeedView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Stores/InteractionStore.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/SpeechHelper.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--entry>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "FurryKids", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "--xcode-version", "16F6", "--platform-family", "iOS", "--deployment-target", "15.0", "--bundle-identifier", "com.example.FurryKids", "--output", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "--target-triple", "arm64-apple-ios15.0-simulator", "--binary-file", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids", "--dependency-file", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "--metadata-file-list", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "signature": "bcb13680e423ee34641ce2b844265e68"}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FurryKids.dst>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-FurryKids-****************************************************************--begin-compiling>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FurryKids.dst>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-FurryKids-****************************************************************--begin-linking>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FurryKids.dst>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--begin-scanning>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--end": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--entry>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib>", "<CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<ExtractAppIntentsMetadata /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Metadata.appintents>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Assets.car", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_signature", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/PkgInfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<Validate /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<ValidateDevelopmentAssets-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_lto.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-non-framework-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist", "<target-FurryKids-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-FurryKids-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-FurryKids-****************************************************************--Barrier-ChangePermissions>", "<target-FurryKids-****************************************************************--Barrier-CodeSign>", "<target-FurryKids-****************************************************************--Barrier-CopyAside>", "<target-FurryKids-****************************************************************--Barrier-GenerateStubAPI>", "<target-FurryKids-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-FurryKids-****************************************************************--Barrier-RegisterProduct>", "<target-FurryKids-****************************************************************--Barrier-StripSymbols>", "<target-FurryKids-****************************************************************--Barrier-Validate>", "<target-FurryKids-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-FurryKids-****************************************************************--CustomTaskProducer>", "<target-FurryKids-****************************************************************--DocumentationTaskProducer>", "<target-FurryKids-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-FurryKids-****************************************************************--GeneratedFilesTaskProducer>", "<target-FurryKids-****************************************************************--HeadermapTaskProducer>", "<target-FurryKids-****************************************************************--InfoPlistTaskProducer>", "<target-FurryKids-****************************************************************--ModuleMapTaskProducer>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--ProductPostprocessingTaskProducer>", "<target-FurryKids-****************************************************************--ProductStructureTaskProducer>", "<target-FurryKids-****************************************************************--RealityAssetsTaskProducer>", "<target-FurryKids-****************************************************************--SanitizerTaskProducer>", "<target-FurryKids-****************************************************************--StubBinaryTaskProducer>", "<target-FurryKids-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-FurryKids-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-FurryKids-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-FurryKids-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-FurryKids-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-FurryKids-****************************************************************--TestHostTaskProducer>", "<target-FurryKids-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-FurryKids-****************************************************************--TestTargetTaskProducer>", "<target-FurryKids-****************************************************************--copy-headers-completion>", "<target-FurryKids-****************************************************************--fused-phase0-link-binary&compile-sources&copy-bundle-resources>", "<target-FurryKids-****************************************************************--generated-headers>", "<target-FurryKids-****************************************************************--swift-generated-headers>"], "outputs": ["<target-FurryKids-****************************************************************--end>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FurryKids.dst>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-FurryKids-****************************************************************--begin-compiling>"], "outputs": ["<target-FurryKids-****************************************************************--entry>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/FurryKids.dst>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-FurryKids-****************************************************************--immediate>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_lto.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList"], "outputs": ["<target-FurryKids-****************************************************************--linker-inputs-ready>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h"], "outputs": ["<target-FurryKids-****************************************************************--modules-ready>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--begin-compiling>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/ssu/root.ssu.yaml", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<ExtractAppIntentsMetadata /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Metadata.appintents>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Assets.car", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_signature", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_lto.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json", "<target-FurryKids-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-FurryKids-****************************************************************--unsigned-product-ready>"]}, "P0:target-FurryKids-****************************************************************-:Debug:Gate target-FurryKids-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-FurryKids-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-FurryKids-****************************************************************--will-sign>"]}, "P0:target-FurryKids-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets/", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets", "--compile", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "15.0", "--platform", "iphonesimulator", "--bundle-identifier", "com.example.FurryKids", "--generate-swift-asset-symbol-extensions", "NO", "--generate-swift-asset-symbols", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "control-enabled": false, "signature": "5c25099af42275575d5bc5bc288ff7f7"}, "P0:target-FurryKids-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets /Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Assets.xcassets/", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned/", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_signature", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Assets.car"], "deps": "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_dependencies"}, "P0:target-FurryKids-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-FurryKids-****************************************************************-:Debug:MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/thinned>"]}, "P0:target-FurryKids-****************************************************************-:Debug:MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_output/unthinned>"]}, "P0:target-FurryKids-****************************************************************-:Debug:MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "inputs": ["<target-FurryKids-****************************************************************--start>", "<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "<MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>", "<TRIGGER: MkDir /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"]}, "P0:target-FurryKids-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/assetcatalog_generated_info.plist", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--entry>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/PkgInfo"]}, "P0:target-FurryKids-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist", "<target-FurryKids-****************************************************************--ProductStructureTaskProducer>", "<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent"]}, "P0:target-FurryKids-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "<target-FurryKids-****************************************************************--ProductStructureTaskProducer>", "<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "-o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "signature": "905007eb07b2f61ad5a6f35fcf079abd"}, "P0:target-FurryKids-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "<target-FurryKids-****************************************************************--Barrier-CodeSign>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"]}, "P0:target-FurryKids-****************************************************************-:Debug:SwiftDriver Compilation FurryKids normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation FurryKids normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/ContentView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/FurryKidsApp.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Pet.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Feed.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Message.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/Color+Extensions.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/FeedView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/ProfileView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/InteractionView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/CreateFeedView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Stores/InteractionStore.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/SpeechHelper.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap", "<ClangStatCache /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-FurryKids-****************************************************************--generated-headers>", "<target-FurryKids-****************************************************************--copy-headers-completion>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.swiftconstvalues", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-FurryKids-****************************************************************-:Debug:Touch /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app": {"tool": "shell", "description": "Touch /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "<target-FurryKids-****************************************************************--Barrier-Validate>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "signature": "d37346c991b464cc4368c4c1fb6ed7df"}, "P0:target-FurryKids-****************************************************************-:Debug:Validate /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/Info.plist", "<target-FurryKids-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-FurryKids-****************************************************************--will-sign>", "<target-FurryKids-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"], "outputs": ["<Validate /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app>"]}, "P0:target-FurryKids-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Preview Content", "<target-FurryKids-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids-e2d40c85f6dcc60effa4c5c463d3c322-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids-e2d40c85f6dcc60effa4c5c463d3c322-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids-e2d40c85f6dcc60effa4c5c463d3c322-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-FurryKids-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-FurryKids-****************************************************************-:Debug:Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo/", "<target-FurryKids-****************************************************************--copy-headers-completion>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-FurryKids-****************************************************************-:Debug:Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json/", "<target-FurryKids-****************************************************************--copy-headers-completion>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-FurryKids-****************************************************************-:Debug:Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc/", "<target-FurryKids-****************************************************************--copy-headers-completion>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-FurryKids-****************************************************************-:Debug:Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule/", "<target-FurryKids-****************************************************************--copy-headers-completion>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-FurryKids-****************************************************************-:Debug:Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids normal": {"tool": "shell", "description": "Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids normal", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids", "<Linked Binary /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids>", "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios15.0-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "signature": "69c4cea959b107ead34d3c227a34c8a2"}, "P2:target-FurryKids-****************************************************************-:Debug:Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib normal", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKidsApp.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Pet.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Message.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/Color+Extensions.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/CreateFeedView.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/InteractionStore.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/SpeechHelper.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "<target-FurryKids-****************************************************************--generated-headers>", "<target-FurryKids-****************************************************************--swift-generated-headers>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_lto.o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios15.0-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList", "-install_name", "@rpath/FurryKids.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/FurryKids.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "deps": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat"], "deps-style": "dependency-info", "signature": "fffd42ce16697ca97ee61b135dd383e9"}, "P2:target-FurryKids-****************************************************************-:Debug:Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib normal", "inputs": ["<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios15.0-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/FurryKids.debug.dylib", "-dead_strip", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Products/Debug-iphonesimulator/FurryKids.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/WorkSpace/furryKids/FurryKids", "signature": "a8297ad5a7be1eb2bf9f64491e9d6487"}, "P2:target-FurryKids-****************************************************************-:Debug:SwiftDriver Compilation Requirements FurryKids normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements FurryKids normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/ContentView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/FurryKidsApp.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Pet.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Feed.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Models/Message.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/Color+Extensions.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/FeedView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/ProfileView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/InteractionView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Views/CreateFeedView.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Stores/InteractionStore.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/FurryKids/Utilities/SpeechHelper.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap", "<ClangStatCache /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-FurryKids-****************************************************************--copy-headers-completion>", "<target-FurryKids-****************************************************************--ModuleVerifierTaskProducer>", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids Swift Compilation Requirements Finished", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftmodule", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftsourceinfo", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.abi.json", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.swiftdoc"]}, "P2:target-FurryKids-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "inputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-Swift.h", "<target-FurryKids-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/FurryKids-Swift.h"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-DebugDylibPath-normal-arm64.txt"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-non-framework-target-headers.hmap", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-non-framework-target-headers.hmap"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-all-target-headers.hmap"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-generated-files.hmap"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-own-target-headers.hmap"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids-project-headers.hmap"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyMetadataFileList"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.DependencyStaticMetadataFileList"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.hmap", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/FurryKids.hmap"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids-OutputFileMap.json"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.LinkFileList"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftConstValuesFileList"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids.SwiftFileList"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/Objects-normal/arm64/FurryKids_const_extract_protocols.json"]}, "P2:target-FurryKids-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist", "inputs": ["<target-FurryKids-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/WorkSpace/furryKids/FurryKids/DerivedData/Build/Intermediates.noindex/FurryKids.build/Debug-iphonesimulator/FurryKids.build/empty-FurryKids.plist"]}}}