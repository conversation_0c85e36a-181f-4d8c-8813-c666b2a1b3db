<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>3F3B8656-BDFE-4E5A-B453-63CDDC4D921D</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>3F3B8656-BDFE-4E5A-B453-63CDDC4D921D.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>1</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FurryKids project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FurryKids</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project FurryKids with scheme FurryKids</string>
			<key>timeStartedRecording</key>
			<real>772623818.15546298</real>
			<key>timeStoppedRecording</key>
			<real>772623827.22158504</real>
			<key>title</key>
			<string>Building project FurryKids with scheme FurryKids</string>
			<key>uniqueIdentifier</key>
			<string>3F3B8656-BDFE-4E5A-B453-63CDDC4D921D</string>
		</dict>
		<key>695EDBD6-3E9E-41AA-B916-02B5E7DEA959</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>695EDBD6-3E9E-41AA-B916-02B5E7DEA959.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FurryKids project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FurryKids</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project FurryKids with scheme FurryKids</string>
			<key>timeStartedRecording</key>
			<real>772558034.56516099</real>
			<key>timeStoppedRecording</key>
			<real>772558045.63319194</real>
			<key>title</key>
			<string>Building project FurryKids with scheme FurryKids</string>
			<key>uniqueIdentifier</key>
			<string>695EDBD6-3E9E-41AA-B916-02B5E7DEA959</string>
		</dict>
	</dict>
</dict>
</plist>
